# Generated by Django 4.2.7 on 2025-05-28 13:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('employees', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AttendancePolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('standard_work_hours', models.DecimalField(decimal_places=2, default=8.0, max_digits=4)),
                ('grace_period_minutes', models.IntegerField(default=15, help_text='Grace period for late arrival')),
                ('minimum_hours_half_day', models.DecimalField(decimal_places=2, default=4.0, max_digits=4)),
                ('lunch_break_minutes', models.IntegerField(default=60)),
                ('tea_break_minutes', models.IntegerField(default=15)),
                ('overtime_rate_multiplier', models.DecimalField(decimal_places=2, default=1.5, max_digits=3)),
                ('max_overtime_hours_per_day', models.DecimalField(decimal_places=2, default=4.0, max_digits=4)),
                ('is_active', models.BooleanField(default=True)),
                ('effective_from', models.DateField(default=django.utils.timezone.now)),
                ('effective_to', models.DateField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Attendance Policy',
                'verbose_name_plural': 'Attendance Policies',
                'ordering': ['-effective_from'],
            },
        ),
        migrations.CreateModel(
            name='Holiday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('date', models.DateField()),
                ('holiday_type', models.CharField(choices=[('public', 'Public Holiday'), ('company', 'Company Holiday'), ('religious', 'Religious Holiday'), ('national', 'National Holiday')], default='public', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('is_recurring', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Holiday',
                'verbose_name_plural': 'Holidays',
                'ordering': ['date'],
                'unique_together': {('name', 'date')},
            },
        ),
        migrations.CreateModel(
            name='AttendanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('check_in_time', models.DateTimeField(blank=True, null=True)),
                ('check_out_time', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('late', 'Late'), ('half_day', 'Half Day'), ('on_leave', 'On Leave'), ('holiday', 'Holiday'), ('weekend', 'Weekend')], default='absent', max_length=20)),
                ('total_hours', models.DecimalField(decimal_places=2, default=0.0, max_digits=4)),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0.0, max_digits=4)),
                ('break_time_minutes', models.IntegerField(default=0)),
                ('notes', models.TextField(blank=True)),
                ('is_manual_entry', models.BooleanField(default=False)),
                ('check_in_location', models.CharField(blank=True, max_length=200)),
                ('check_out_location', models.CharField(blank=True, max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_attendance_records', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='employees.employee')),
            ],
            options={
                'verbose_name': 'Attendance Record',
                'verbose_name_plural': 'Attendance Records',
                'ordering': ['-date', 'employee'],
                'unique_together': {('employee', 'date')},
            },
        ),
        migrations.CreateModel(
            name='AttendanceBreak',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('break_type', models.CharField(choices=[('lunch', 'Lunch Break'), ('tea', 'Tea Break'), ('prayer', 'Prayer Break'), ('personal', 'Personal Break'), ('meeting', 'Meeting Break')], default='lunch', max_length=20)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('duration_minutes', models.IntegerField(default=0)),
                ('notes', models.CharField(blank=True, max_length=200)),
                ('attendance_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='breaks', to='attendance.attendancerecord')),
            ],
            options={
                'verbose_name': 'Attendance Break',
                'verbose_name_plural': 'Attendance Breaks',
                'ordering': ['start_time'],
            },
        ),
    ]
