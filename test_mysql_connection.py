#!/usr/bin/env python3
"""
Test MySQL connection for Ethiopian Electric Utility Attendance System
This script tests the MySQL database connection and configuration.
"""

import os
import sys
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eeu_attendance.settings')

try:
    import django
    django.setup()
    
    from django.db import connection
    from django.core.management.color import make_style
    from django.conf import settings
    
    style = make_style()
    
    def test_database_connection():
        """Test database connection"""
        print("🔍 Testing MySQL Database Connection...")
        print("=" * 50)
        
        try:
            # Test basic connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
            if result and result[0] == 1:
                print(style.SUCCESS("✅ Database connection successful!"))
                return True
            else:
                print(style.ERROR("❌ Database connection failed!"))
                return False
                
        except Exception as e:
            print(style.ERROR(f"❌ Database connection error: {e}"))
            return False
    
    def check_database_info():
        """Check database information"""
        print("\n📊 Database Information:")
        print("-" * 30)
        
        try:
            with connection.cursor() as cursor:
                # Check MySQL version
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                print(f"MySQL Version: {version}")
                
                # Check database name
                cursor.execute("SELECT DATABASE()")
                db_name = cursor.fetchone()[0]
                print(f"Database Name: {db_name}")
                
                # Check character set
                cursor.execute("SELECT @@character_set_database, @@collation_database")
                charset, collation = cursor.fetchone()
                print(f"Character Set: {charset}")
                print(f"Collation: {collation}")
                
                # Check timezone
                cursor.execute("SELECT @@time_zone")
                timezone = cursor.fetchone()[0]
                print(f"MySQL Timezone: {timezone}")
                
        except Exception as e:
            print(style.ERROR(f"❌ Error getting database info: {e}"))
    
    def check_tables():
        """Check if tables exist"""
        print("\n📋 Database Tables:")
        print("-" * 20)
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if tables:
                    print(f"Found {len(tables)} tables:")
                    for table in tables:
                        print(f"  - {table[0]}")
                else:
                    print("No tables found. Run migrations first:")
                    print("  python manage.py migrate")
                    
        except Exception as e:
            print(style.ERROR(f"❌ Error checking tables: {e}"))
    
    def check_django_settings():
        """Check Django database settings"""
        print("\n⚙️  Django Database Settings:")
        print("-" * 30)
        
        db_config = settings.DATABASES['default']
        print(f"Engine: {db_config['ENGINE']}")
        print(f"Name: {db_config['NAME']}")
        print(f"User: {db_config['USER']}")
        print(f"Host: {db_config['HOST']}")
        print(f"Port: {db_config['PORT']}")
        
        if 'OPTIONS' in db_config:
            print("Options:")
            for key, value in db_config['OPTIONS'].items():
                print(f"  {key}: {value}")
    
    def test_timezone():
        """Test timezone configuration"""
        print("\n🌍 Timezone Configuration:")
        print("-" * 25)
        
        from django.utils import timezone
        import pytz
        
        print(f"Django TIME_ZONE: {settings.TIME_ZONE}")
        print(f"Django USE_TZ: {settings.USE_TZ}")
        
        # Current time in different timezones
        now_utc = timezone.now()
        print(f"Current UTC time: {now_utc}")
        
        # Convert to Ethiopian time
        et_tz = pytz.timezone('Africa/Addis_Ababa')
        now_et = now_utc.astimezone(et_tz)
        print(f"Current Ethiopian time: {now_et}")
    
    def main():
        """Main test function"""
        print("🧪 MySQL Connection Test")
        print("Ethiopian Electric Utility - Attendance Management System")
        print("=" * 60)
        
        # Test connection
        if not test_database_connection():
            print("\n💡 Troubleshooting Tips:")
            print("1. Ensure MySQL server is running")
            print("2. Check database credentials in .env file")
            print("3. Verify database and user exist")
            print("4. Check MySQL client library installation")
            sys.exit(1)
        
        # Additional checks
        check_django_settings()
        check_database_info()
        check_tables()
        test_timezone()
        
        print("\n" + "=" * 60)
        print(style.SUCCESS("🎉 All tests completed successfully!"))
        print("\nNext steps:")
        print("1. Run migrations: python manage.py migrate")
        print("2. Create superuser: python manage.py createsuperuser")
        print("3. Load sample data: python manage.py create_sample_data")
        print("4. Start server: python manage.py runserver")
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\nMake sure you have:")
    print("1. Activated virtual environment")
    print("2. Installed requirements: pip install -r requirements.txt")
    print("3. Created .env file with database settings")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
