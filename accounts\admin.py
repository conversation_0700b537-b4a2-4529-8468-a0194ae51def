from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, UserProfile


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Custom User Admin for Ethiopian Electric Utility
    """
    list_display = ('email', 'username', 'first_name', 'last_name', 'employee_id', 'role', 'is_active', 'date_joined')
    list_filter = ('role', 'is_active', 'is_staff', 'is_superuser', 'date_joined')
    search_fields = ('email', 'username', 'first_name', 'last_name', 'employee_id')
    ordering = ('email',)
    filter_horizontal = ('groups', 'user_permissions')
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('Personal info'), {
            'fields': ('first_name', 'last_name', 'email', 'employee_id', 'phone_number', 
                      'full_name_amharic', 'profile_picture')
        }),
        (_('Permissions'), {
            'fields': ('role', 'is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'first_name', 'last_name', 'employee_id', 
                      'role', 'password1', 'password2'),
        }),
    )


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    User Profile Admin
    """
    list_display = ('user', 'location', 'birth_date', 'emergency_contact_name', 'created_at')
    list_filter = ('location', 'created_at')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'emergency_contact_name')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('User Information'), {
            'fields': ('user',)
        }),
        (_('Personal Details'), {
            'fields': ('bio', 'location', 'birth_date')
        }),
        (_('Emergency Contact'), {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
