from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from datetime import datetime, date, timedelta
from .models import LeaveType, LeaveBalance, LeaveRequest, LeaveApprovalWorkflow
from employees.models import Employee


@login_required
def my_leaves_view(request):
    """Employee's leave requests and balances"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, 'Employee profile not found. Please contact HR.')
        return redirect('home')
    
    # Get leave requests
    leave_requests = LeaveRequest.objects.filter(employee=employee).order_by('-applied_on')
    
    # Get leave balances for current year
    current_year = timezone.now().year
    leave_balances = LeaveBalance.objects.filter(employee=employee, year=current_year)
    
    # Pagination for leave requests
    paginator = Paginator(leave_requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'leave_balances': leave_balances,
        'current_year': current_year,
    }
    return render(request, 'leaves/my_leaves.html', context)


@login_required
def leave_request_view(request):
    """Create new leave request"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, 'Employee profile not found. Please contact HR.')
        return redirect('home')
    
    if request.method == 'POST':
        leave_type_id = request.POST.get('leave_type')
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')
        duration_type = request.POST.get('duration_type', 'full_day')
        reason = request.POST.get('reason')
        emergency_contact = request.POST.get('emergency_contact', '')
        emergency_phone = request.POST.get('emergency_phone', '')
        
        try:
            leave_type = LeaveType.objects.get(id=leave_type_id, is_active=True)
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            # Validate dates
            if start_date < timezone.now().date():
                messages.error(request, 'Start date cannot be in the past.')
                return redirect('leaves:request')
            
            if end_date < start_date:
                messages.error(request, 'End date cannot be before start date.')
                return redirect('leaves:request')
            
            # Check minimum notice period
            days_notice = (start_date - timezone.now().date()).days
            if days_notice < leave_type.min_notice_days:
                messages.error(request, f'This leave type requires at least {leave_type.min_notice_days} days notice.')
                return redirect('leaves:request')
            
            # Check for overlapping requests
            overlapping = LeaveRequest.objects.filter(
                employee=employee,
                status__in=['pending', 'approved'],
                start_date__lte=end_date,
                end_date__gte=start_date
            ).exists()
            
            if overlapping:
                messages.error(request, 'You have overlapping leave requests.')
                return redirect('leaves:request')
            
            # Create leave request
            leave_request = LeaveRequest.objects.create(
                employee=employee,
                leave_type=leave_type,
                start_date=start_date,
                end_date=end_date,
                duration_type=duration_type,
                reason=reason,
                emergency_contact=emergency_contact,
                emergency_phone=emergency_phone,
            )
            
            # Create approval workflow (simplified - just supervisor approval)
            if employee.supervisor:
                LeaveApprovalWorkflow.objects.create(
                    leave_request=leave_request,
                    approver=employee.supervisor.user,
                    step_order=1,
                    is_required=True
                )
            
            messages.success(request, 'Leave request submitted successfully.')
            return redirect('leaves:my_leaves')
            
        except (ValueError, LeaveType.DoesNotExist) as e:
            messages.error(request, 'Invalid data provided.')
            return redirect('leaves:request')
    
    # Get available leave types
    leave_types = LeaveType.objects.filter(is_active=True)
    
    # Get leave balances for current year
    current_year = timezone.now().year
    leave_balances = LeaveBalance.objects.filter(employee=employee, year=current_year)
    
    context = {
        'leave_types': leave_types,
        'leave_balances': leave_balances,
    }
    return render(request, 'leaves/request.html', context)


@login_required
def leave_detail_view(request, request_id):
    """Detailed view of a leave request"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, 'Employee profile not found.')
        return redirect('home')
    
    leave_request = get_object_or_404(LeaveRequest, id=request_id, employee=employee)
    approval_steps = LeaveApprovalWorkflow.objects.filter(leave_request=leave_request).order_by('step_order')
    
    context = {
        'leave_request': leave_request,
        'approval_steps': approval_steps,
    }
    return render(request, 'leaves/detail.html', context)


@login_required
def cancel_leave_request(request, request_id):
    """Cancel a pending leave request"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, 'Employee profile not found.')
        return redirect('home')
    
    leave_request = get_object_or_404(
        LeaveRequest, 
        id=request_id, 
        employee=employee,
        status='pending'
    )
    
    if request.method == 'POST':
        leave_request.status = 'cancelled'
        leave_request.save()
        messages.success(request, 'Leave request cancelled successfully.')
    
    return redirect('leaves:my_leaves')


@login_required
def all_leave_requests_view(request):
    """HR view of all leave requests (requires staff permission)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to view this page.')
        return redirect('home')
    
    # Get filter parameters
    search = request.GET.get('search', '')
    department = request.GET.get('department', '')
    status = request.GET.get('status', '')
    leave_type = request.GET.get('leave_type', '')
    
    # Base queryset
    leave_requests = LeaveRequest.objects.select_related(
        'employee__user', 'employee__department', 'leave_type'
    ).order_by('-applied_on')
    
    # Apply filters
    if search:
        leave_requests = leave_requests.filter(
            Q(employee__employee_id__icontains=search) |
            Q(employee__user__first_name__icontains=search) |
            Q(employee__user__last_name__icontains=search)
        )
    
    if department:
        leave_requests = leave_requests.filter(employee__department_id=department)
    
    if status:
        leave_requests = leave_requests.filter(status=status)
    
    if leave_type:
        leave_requests = leave_requests.filter(leave_type_id=leave_type)
    
    # Pagination
    paginator = Paginator(leave_requests, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    from employees.models import Department
    departments = Department.objects.filter(is_active=True)
    leave_types = LeaveType.objects.filter(is_active=True)
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'leave_types': leave_types,
        'search': search,
        'department': department,
        'status': status,
        'leave_type': leave_type,
        'status_choices': LeaveRequest.STATUS_CHOICES,
    }
    return render(request, 'leaves/all_requests.html', context)


@login_required
def approve_leave_request(request, request_id):
    """Approve or reject a leave request (for supervisors/HR)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to perform this action.')
        return redirect('home')
    
    leave_request = get_object_or_404(LeaveRequest, id=request_id, status='pending')
    
    if request.method == 'POST':
        action = request.POST.get('action')
        comments = request.POST.get('comments', '')
        
        if action == 'approve':
            leave_request.status = 'approved'
            leave_request.approved_by = request.user
            leave_request.approved_on = timezone.now()
            leave_request.save()
            
            # Update leave balance
            current_year = timezone.now().year
            leave_balance, created = LeaveBalance.objects.get_or_create(
                employee=leave_request.employee,
                leave_type=leave_request.leave_type,
                year=current_year,
                defaults={'allocated_days': leave_request.leave_type.max_days_per_year}
            )
            leave_balance.used_days += leave_request.total_days
            leave_balance.save()
            
            messages.success(request, 'Leave request approved successfully.')
            
        elif action == 'reject':
            leave_request.status = 'rejected'
            leave_request.rejection_reason = comments
            leave_request.save()
            messages.success(request, 'Leave request rejected.')
        
        # Update approval workflow
        approval_step = LeaveApprovalWorkflow.objects.filter(
            leave_request=leave_request,
            approver=request.user
        ).first()
        
        if approval_step:
            approval_step.approved = (action == 'approve')
            approval_step.approved_on = timezone.now()
            approval_step.comments = comments
            approval_step.save()
    
    return redirect('leaves:all_requests')
