/* Ethiopian Electric Utility - Custom Styles */

:root {
    --eeu-primary: #1e3c72;
    --eeu-secondary: #2a5298;
    --eeu-success: #009639;
    --eeu-warning: #FFDE00;
    --eeu-danger: #DA020E;
    --eeu-light: #f8f9fa;
    --eeu-dark: #212529;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Navigation Enhancements */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

/* Ethiopian Flag Colors */
.ethiopian-flag-green { background-color: var(--eeu-success); }
.ethiopian-flag-yellow { background-color: var(--eeu-warning); }
.ethiopian-flag-red { background-color: var(--eeu-danger); }

/* Utility Classes */
.text-eeu-primary { color: var(--eeu-primary) !important; }
.text-eeu-secondary { color: var(--eeu-secondary) !important; }
.bg-eeu-primary { background-color: var(--eeu-primary) !important; }
.bg-eeu-secondary { background-color: var(--eeu-secondary) !important; }

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .hero-section {
        padding: 40px 0 !important;
    }
    
    .hero-section h1 {
        font-size: 2rem !important;
    }
    
    .quick-action-btn {
        margin-bottom: 10px;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--eeu-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--eeu-secondary);
}

/* Footer Enhancements */
footer {
    background: linear-gradient(135deg, var(--eeu-primary) 0%, var(--eeu-secondary) 100%);
    color: white;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 10px;
    border-left: 4px solid;
}

.alert-success {
    border-left-color: var(--eeu-success);
    background-color: rgba(0, 150, 57, 0.1);
}

.alert-info {
    border-left-color: var(--eeu-primary);
    background-color: rgba(30, 60, 114, 0.1);
}

.alert-warning {
    border-left-color: var(--eeu-warning);
    background-color: rgba(255, 222, 0, 0.1);
}

.alert-danger {
    border-left-color: var(--eeu-danger);
    background-color: rgba(218, 2, 14, 0.1);
}

/* Form Enhancements */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--eeu-primary);
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

/* Table Enhancements */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, var(--eeu-primary) 0%, var(--eeu-secondary) 100%);
    color: white;
    border: none;
    font-weight: 600;
}

/* Badge Enhancements */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

/* List Group Enhancements */
.list-group-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

/* Progress Bar Enhancements */
.progress {
    border-radius: 10px;
    height: 8px;
}

.progress-bar {
    border-radius: 10px;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}
