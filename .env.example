# Database Configuration for MySQL
DB_NAME=eeu_attendance_db
DB_USER=eeu_user
DB_PASSWORD=your_secure_password_here
DB_HOST=localhost
DB_PORT=3306

# Django Configuration
SECRET_KEY=your-very-long-secret-key-here-change-this-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Email Configuration (Optional - for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security Settings (Production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False

# Logging
LOG_LEVEL=INFO
