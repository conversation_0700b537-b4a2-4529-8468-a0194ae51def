from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Count, Sum, Q
from datetime import datetime, timedelta
from attendance.models import AttendanceRecord
from leaves.models import LeaveRequest, LeaveBalance
from employees.models import Employee


@login_required
def dashboard_view(request):
    """Main dashboard view"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, 'Employee profile not found. Please contact HR.')
        return redirect('home')
    
    # Get today's attendance
    today = timezone.now().date()
    today_attendance = AttendanceRecord.objects.filter(
        employee=employee, date=today
    ).first()
    
    # Get current month statistics
    current_month = timezone.now().replace(day=1)
    next_month = (current_month + timedelta(days=32)).replace(day=1)
    
    monthly_stats = AttendanceRecord.objects.filter(
        employee=employee,
        date__gte=current_month.date(),
        date__lt=next_month.date()
    ).aggregate(
        total_days=Count('id'),
        present_days=Count('id', filter=Q(status='present')),
        late_days=Count('id', filter=Q(status='late')),
        total_hours=Sum('total_hours'),
        total_overtime=Sum('overtime_hours')
    )
    
    # Get leave balances for current year
    current_year = timezone.now().year
    leave_balances = LeaveBalance.objects.filter(
        employee=employee, year=current_year
    )
    
    # Get recent attendance (last 5 days)
    recent_attendance = AttendanceRecord.objects.filter(
        employee=employee
    ).order_by('-date')[:5]
    
    # Get recent leave requests (last 3)
    recent_leaves = LeaveRequest.objects.filter(
        employee=employee
    ).order_by('-applied_on')[:3]
    
    # Calculate attendance rate
    attendance_rate = 0
    if monthly_stats['total_days'] > 0:
        attendance_rate = round(
            (monthly_stats['present_days'] / monthly_stats['total_days']) * 100, 1
        )
    
    context = {
        'employee': employee,
        'today_attendance': today_attendance,
        'monthly_stats': monthly_stats,
        'leave_balances': leave_balances,
        'recent_attendance': recent_attendance,
        'recent_leaves': recent_leaves,
        'attendance_rate': attendance_rate,
        'current_time': timezone.now(),
    }
    
    # Add HR-specific data if user is staff
    if request.user.is_staff:
        # Total employees
        total_employees = Employee.objects.filter(employment_status='active').count()
        
        # Today's overall attendance
        today_overall = AttendanceRecord.objects.filter(date=today).aggregate(
            total_records=Count('id'),
            present_count=Count('id', filter=Q(status='present')),
            late_count=Count('id', filter=Q(status='late')),
            absent_count=Count('id', filter=Q(status='absent'))
        )
        
        # Pending leave requests
        pending_leaves = LeaveRequest.objects.filter(status='pending').count()
        
        context.update({
            'is_hr_view': True,
            'total_employees': total_employees,
            'today_overall': today_overall,
            'pending_leaves': pending_leaves,
        })
    
    return render(request, 'dashboard/dashboard.html', context)


@login_required
def dashboard_stats_api(request):
    """API endpoint for dashboard statistics"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        return JsonResponse({'error': 'Employee profile not found'}, status=404)
    
    # Get today's attendance status
    today = timezone.now().date()
    today_attendance = AttendanceRecord.objects.filter(
        employee=employee, date=today
    ).first()
    
    # Get current month hours
    current_month = timezone.now().replace(day=1)
    next_month = (current_month + timedelta(days=32)).replace(day=1)
    
    monthly_hours = AttendanceRecord.objects.filter(
        employee=employee,
        date__gte=current_month.date(),
        date__lt=next_month.date()
    ).aggregate(
        total_hours=Sum('total_hours'),
        total_overtime=Sum('overtime_hours')
    )
    
    data = {
        'clocked_in': bool(today_attendance and today_attendance.check_in_time),
        'clocked_out': bool(today_attendance and today_attendance.check_out_time),
        'today_hours': float(today_attendance.total_hours) if today_attendance else 0,
        'monthly_hours': float(monthly_hours['total_hours'] or 0),
        'monthly_overtime': float(monthly_hours['total_overtime'] or 0),
        'current_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
    }
    
    return JsonResponse(data)
