from django.contrib import admin
from .models import AttendanceRecord, AttendanceBreak, Holiday, AttendancePolicy


@admin.register(AttendanceRecord)
class AttendanceRecordAdmin(admin.ModelAdmin):
    list_display = ('employee', 'date', 'check_in_time', 'check_out_time', 'status', 'total_hours', 'overtime_hours')
    list_filter = ('status', 'date', 'is_manual_entry')
    search_fields = ('employee__employee_id', 'employee__user__first_name', 'employee__user__last_name')
    ordering = ('-date', 'employee')
    readonly_fields = ('total_hours', 'overtime_hours', 'created_at', 'updated_at')


@admin.register(AttendanceBreak)
class AttendanceBreakAdmin(admin.ModelAdmin):
    list_display = ('attendance_record', 'break_type', 'start_time', 'end_time', 'duration_minutes')
    list_filter = ('break_type', 'start_time')
    search_fields = ('attendance_record__employee__employee_id',)
    ordering = ('-start_time',)


@admin.register(Holiday)
class HolidayAdmin(admin.ModelAdmin):
    list_display = ('name', 'date', 'holiday_type', 'is_recurring', 'is_active')
    list_filter = ('holiday_type', 'is_recurring', 'is_active', 'date')
    search_fields = ('name', 'description')
    ordering = ('date',)


@admin.register(AttendancePolicy)
class AttendancePolicyAdmin(admin.ModelAdmin):
    list_display = ('name', 'standard_work_hours', 'grace_period_minutes', 'is_active', 'effective_from')
    list_filter = ('is_active', 'effective_from')
    search_fields = ('name', 'description')
    ordering = ('-effective_from',)
