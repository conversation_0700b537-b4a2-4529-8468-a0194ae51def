#!/usr/bin/env python3
"""
Setup MySQL database for Django EEU Attendance System
This script creates the 'dave_test' database if it doesn't exist.
"""

import pymysql
import sys
import os

def create_database():
    """Create the dave_test database if it doesn't exist"""

    # Database connection parameters - try different common configurations
    password_options = ['', 'root', 'password', '123456', 'admin']

    for password in password_options:
        db_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': password,
            'charset': 'utf8mb4'
        }

        try:
            print(f"🔍 Trying to connect with password: {'(empty)' if not password else '***'}")
            connection = pymysql.connect(**db_config)
            print(f"✅ Connected successfully with password: {'(empty)' if not password else '***'}")
            break
        except pymysql.Error as e:
            if "Access denied" in str(e):
                continue
            else:
                raise e
    else:
        raise pymysql.Error("Could not connect with any common password. Please check MySQL configuration.")

    try:
        cursor = connection.cursor()

        # Check MySQL version
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"✅ Connected to MySQL version: {version}")

        # Check if database exists
        cursor.execute("SHOW DATABASES LIKE 'dave_test'")
        result = cursor.fetchone()

        if result:
            print("✅ Database 'dave_test' already exists")
        else:
            print("📝 Creating database 'dave_test'...")
            # Create database with UTF-8 support
            cursor.execute("""
                CREATE DATABASE dave_test
                CHARACTER SET utf8mb4
                COLLATE utf8mb4_unicode_ci
            """)
            print("✅ Database 'dave_test' created successfully")

        # Show all databases to verify
        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()
        print("\n📋 Available databases:")
        for db in databases:
            marker = "👉" if db[0] == 'dave_test' else "  "
            print(f"{marker} {db[0]}")

        cursor.close()
        connection.close()
        print("\n🎉 Database setup completed successfully!")
        return True

    except pymysql.Error as e:
        print(f"❌ MySQL Error: {e}")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure MySQL server is running")
        print("2. Check if root user has no password (default)")
        print("3. If root has a password, update the script with correct credentials")
        print("4. Ensure MySQL server is accessible on localhost:3306")
        return False

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_django_connection():
    """Test Django database connection"""
    print("\n🧪 Testing Django database connection...")

    # Add current directory to Python path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

    try:
        # Set Django settings
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eeu_attendance.settings')

        import django
        django.setup()

        from django.db import connection

        # Test connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()

        if result and result[0] == 1:
            print("✅ Django can connect to MySQL database successfully!")

            # Get database info
            with connection.cursor() as cursor:
                cursor.execute("SELECT DATABASE(), VERSION()")
                db_name, version = cursor.fetchone()
                print(f"📊 Connected to database: {db_name}")
                print(f"📊 MySQL version: {version}")

            return True
        else:
            print("❌ Django database connection test failed")
            return False

    except Exception as e:
        print(f"❌ Django connection error: {e}")
        print("💡 This might be normal if Django apps aren't configured yet")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 MySQL Database Setup for Django EEU Attendance System")
    print("=" * 60)

    # Step 1: Create database
    if create_database():
        # Step 2: Test Django connection
        test_django_connection()

        print("\n" + "=" * 60)
        print("✅ Setup completed! Next steps:")
        print("1. Run: python manage.py migrate")
        print("2. Run: python manage.py createsuperuser")
        print("3. Run: python manage.py runserver")
        print("=" * 60)
    else:
        print("\n❌ Database setup failed. Please check MySQL installation and configuration.")
        sys.exit(1)
