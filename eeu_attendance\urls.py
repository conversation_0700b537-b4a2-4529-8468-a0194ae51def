"""
URL configuration for eeu_attendance project.

Ethiopian Electric Utility Employee Attendance Management System
"""
from django.contrib import admin
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from datetime import datetime

def home_view(request):
    """Professional home page view"""
    context = {
        'current_time': timezone.now(),
        'system_status': 'operational',
        'total_employees': 150,  # This would come from database in real implementation
        'active_sessions': 45,   # This would come from database in real implementation
    }
    return render(request, 'home/index.html', context)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', home_view, name='home'),
    # Additional URLs will be added after creating the apps
    # path('accounts/', include('accounts.urls')),
    # path('employees/', include('employees.urls')),
    # path('attendance/', include('attendance.urls')),
    # path('leaves/', include('leaves.urls')),
    # path('reports/', include('reports.urls')),
    # path('dashboard/', include('dashboard.urls')),
    # path('api/', include('api.urls')),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
