from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from datetime import datetime, timedelta
from employees.models import Employee


class AttendanceRecord(models.Model):
    """
    Daily attendance record for employees
    """
    STATUS_CHOICES = [
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
        ('half_day', 'Half Day'),
        ('on_leave', 'On Leave'),
        ('holiday', 'Holiday'),
        ('weekend', 'Weekend'),
    ]
    
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='attendance_records')
    date = models.DateField()
    check_in_time = models.DateTimeField(null=True, blank=True)
    check_out_time = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='absent')
    
    # Calculated fields
    total_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0.0)
    overtime_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0.0)
    break_time_minutes = models.IntegerField(default=0)
    
    # Additional information
    notes = models.TextField(blank=True)
    is_manual_entry = models.BooleanField(default=False)
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_attendance_records'
    )
    
    # Location tracking (optional)
    check_in_location = models.CharField(max_length=200, blank=True)
    check_out_location = models.CharField(max_length=200, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Attendance Record'
        verbose_name_plural = 'Attendance Records'
        unique_together = ['employee', 'date']
        ordering = ['-date', 'employee']
    
    def __str__(self):
        return f"{self.employee} - {self.date} ({self.status})"
    
    def save(self, *args, **kwargs):
        # Calculate total hours if both check-in and check-out times are available
        if self.check_in_time and self.check_out_time:
            time_diff = self.check_out_time - self.check_in_time
            total_seconds = time_diff.total_seconds()
            # Subtract break time
            total_seconds -= (self.break_time_minutes * 60)
            self.total_hours = round(total_seconds / 3600, 2)
            
            # Calculate overtime (assuming 8 hours is standard)
            standard_hours = self.employee.work_hours_per_day
            if self.total_hours > standard_hours:
                self.overtime_hours = self.total_hours - standard_hours
            
            # Update status based on timing
            if self.total_hours >= standard_hours:
                self.status = 'present'
            elif self.total_hours >= (standard_hours / 2):
                self.status = 'half_day'
            else:
                self.status = 'late'
        
        super().save(*args, **kwargs)
    
    @property
    def is_late(self):
        if not self.check_in_time:
            return False
        # Assuming work starts at 8:00 AM
        expected_start = self.check_in_time.replace(hour=8, minute=0, second=0, microsecond=0)
        return self.check_in_time > expected_start
    
    @property
    def late_minutes(self):
        if not self.is_late:
            return 0
        expected_start = self.check_in_time.replace(hour=8, minute=0, second=0, microsecond=0)
        return int((self.check_in_time - expected_start).total_seconds() / 60)


class AttendanceBreak(models.Model):
    """
    Break time tracking within attendance records
    """
    BREAK_TYPE_CHOICES = [
        ('lunch', 'Lunch Break'),
        ('tea', 'Tea Break'),
        ('prayer', 'Prayer Break'),
        ('personal', 'Personal Break'),
        ('meeting', 'Meeting Break'),
    ]
    
    attendance_record = models.ForeignKey(
        AttendanceRecord, 
        on_delete=models.CASCADE, 
        related_name='breaks'
    )
    break_type = models.CharField(max_length=20, choices=BREAK_TYPE_CHOICES, default='lunch')
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    duration_minutes = models.IntegerField(default=0)
    notes = models.CharField(max_length=200, blank=True)
    
    class Meta:
        verbose_name = 'Attendance Break'
        verbose_name_plural = 'Attendance Breaks'
        ordering = ['start_time']
    
    def save(self, *args, **kwargs):
        if self.start_time and self.end_time:
            time_diff = self.end_time - self.start_time
            self.duration_minutes = int(time_diff.total_seconds() / 60)
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.attendance_record.employee} - {self.break_type} ({self.duration_minutes} min)"


class Holiday(models.Model):
    """
    Public holidays and company holidays
    """
    HOLIDAY_TYPE_CHOICES = [
        ('public', 'Public Holiday'),
        ('company', 'Company Holiday'),
        ('religious', 'Religious Holiday'),
        ('national', 'National Holiday'),
    ]
    
    name = models.CharField(max_length=100)
    date = models.DateField()
    holiday_type = models.CharField(max_length=20, choices=HOLIDAY_TYPE_CHOICES, default='public')
    description = models.TextField(blank=True)
    is_recurring = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Holiday'
        verbose_name_plural = 'Holidays'
        unique_together = ['name', 'date']
        ordering = ['date']
    
    def __str__(self):
        return f"{self.name} - {self.date}"


class AttendancePolicy(models.Model):
    """
    Attendance policies and rules
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    
    # Time settings
    standard_work_hours = models.DecimalField(max_digits=4, decimal_places=2, default=8.0)
    grace_period_minutes = models.IntegerField(default=15, help_text="Grace period for late arrival")
    minimum_hours_half_day = models.DecimalField(max_digits=4, decimal_places=2, default=4.0)
    
    # Break settings
    lunch_break_minutes = models.IntegerField(default=60)
    tea_break_minutes = models.IntegerField(default=15)
    
    # Overtime settings
    overtime_rate_multiplier = models.DecimalField(max_digits=3, decimal_places=2, default=1.5)
    max_overtime_hours_per_day = models.DecimalField(max_digits=4, decimal_places=2, default=4.0)
    
    is_active = models.BooleanField(default=True)
    effective_from = models.DateField(default=timezone.now)
    effective_to = models.DateField(null=True, blank=True)
    
    class Meta:
        verbose_name = 'Attendance Policy'
        verbose_name_plural = 'Attendance Policies'
        ordering = ['-effective_from']
    
    def __str__(self):
        return self.name
