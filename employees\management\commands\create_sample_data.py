from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from accounts.models import User
from employees.models import Department, Position, Employee
from leaves.models import LeaveType, LeaveBalance
from attendance.models import AttendanceRecord, Holiday


class Command(BaseCommand):
    help = 'Create sample data for Ethiopian Electric Utility Attendance System'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample data...'))
        
        # Create Departments
        self.create_departments()
        
        # Create Positions
        self.create_positions()
        
        # Create Leave Types
        self.create_leave_types()
        
        # Create Holidays
        self.create_holidays()
        
        # Create Sample Employees
        self.create_employees()
        
        # Create Leave Balances
        self.create_leave_balances()
        
        # Create Sample Attendance Records
        self.create_attendance_records()
        
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))

    def create_departments(self):
        departments = [
            {'name': 'Human Resources', 'code': 'HR', 'description': 'Human Resources Department'},
            {'name': 'Information Technology', 'code': 'IT', 'description': 'IT Department'},
            {'name': 'Finance', 'code': 'FIN', 'description': 'Finance Department'},
            {'name': 'Operations', 'code': 'OPS', 'description': 'Operations Department'},
            {'name': 'Engineering', 'code': 'ENG', 'description': 'Engineering Department'},
            {'name': 'Customer Service', 'code': 'CS', 'description': 'Customer Service Department'},
        ]
        
        for dept_data in departments:
            dept, created = Department.objects.get_or_create(
                code=dept_data['code'],
                defaults=dept_data
            )
            if created:
                self.stdout.write(f'Created department: {dept.name}')

    def create_positions(self):
        positions = [
            {'title': 'HR Manager', 'code': 'HR-MGR', 'department_code': 'HR', 'level': 'manager'},
            {'title': 'HR Officer', 'code': 'HR-OFF', 'department_code': 'HR', 'level': 'officer'},
            {'title': 'IT Manager', 'code': 'IT-MGR', 'department_code': 'IT', 'level': 'manager'},
            {'title': 'Software Developer', 'code': 'IT-DEV', 'department_code': 'IT', 'level': 'specialist'},
            {'title': 'System Administrator', 'code': 'IT-SYS', 'department_code': 'IT', 'level': 'specialist'},
            {'title': 'Finance Manager', 'code': 'FIN-MGR', 'department_code': 'FIN', 'level': 'manager'},
            {'title': 'Accountant', 'code': 'FIN-ACC', 'department_code': 'FIN', 'level': 'officer'},
            {'title': 'Operations Manager', 'code': 'OPS-MGR', 'department_code': 'OPS', 'level': 'manager'},
            {'title': 'Operations Officer', 'code': 'OPS-OFF', 'department_code': 'OPS', 'level': 'officer'},
            {'title': 'Electrical Engineer', 'code': 'ENG-ELE', 'department_code': 'ENG', 'level': 'specialist'},
            {'title': 'Customer Service Rep', 'code': 'CS-REP', 'department_code': 'CS', 'level': 'officer'},
        ]
        
        for pos_data in positions:
            department = Department.objects.get(code=pos_data['department_code'])
            pos, created = Position.objects.get_or_create(
                code=pos_data['code'],
                defaults={
                    'title': pos_data['title'],
                    'department': department,
                    'level': pos_data['level'],
                    'description': f'{pos_data["title"]} in {department.name}'
                }
            )
            if created:
                self.stdout.write(f'Created position: {pos.title}')

    def create_leave_types(self):
        leave_types = [
            {
                'name': 'Annual Leave',
                'code': 'ANNUAL',
                'max_days_per_year': 30,
                'is_paid': True,
                'requires_approval': True,
                'min_notice_days': 7,
                'description': 'Annual vacation leave'
            },
            {
                'name': 'Sick Leave',
                'code': 'SICK',
                'max_days_per_year': 15,
                'is_paid': True,
                'requires_approval': False,
                'min_notice_days': 0,
                'description': 'Medical leave for illness'
            },
            {
                'name': 'Maternity Leave',
                'code': 'MATERNITY',
                'max_days_per_year': 120,
                'is_paid': True,
                'requires_approval': True,
                'min_notice_days': 30,
                'is_gender_specific': True,
                'description': 'Maternity leave for female employees'
            },
            {
                'name': 'Paternity Leave',
                'code': 'PATERNITY',
                'max_days_per_year': 10,
                'is_paid': True,
                'requires_approval': True,
                'min_notice_days': 7,
                'is_gender_specific': True,
                'description': 'Paternity leave for male employees'
            },
            {
                'name': 'Emergency Leave',
                'code': 'EMERGENCY',
                'max_days_per_year': 5,
                'is_paid': True,
                'requires_approval': True,
                'min_notice_days': 0,
                'description': 'Emergency leave for urgent situations'
            },
        ]
        
        for leave_data in leave_types:
            leave_type, created = LeaveType.objects.get_or_create(
                code=leave_data['code'],
                defaults=leave_data
            )
            if created:
                self.stdout.write(f'Created leave type: {leave_type.name}')

    def create_holidays(self):
        current_year = timezone.now().year
        holidays = [
            {'name': 'New Year', 'date': f'{current_year}-01-01', 'holiday_type': 'national'},
            {'name': 'Epiphany', 'date': f'{current_year}-01-19', 'holiday_type': 'religious'},
            {'name': 'Victory of Adwa', 'date': f'{current_year}-03-02', 'holiday_type': 'national'},
            {'name': 'Good Friday', 'date': f'{current_year}-04-14', 'holiday_type': 'religious'},
            {'name': 'Easter Sunday', 'date': f'{current_year}-04-16', 'holiday_type': 'religious'},
            {'name': 'Labour Day', 'date': f'{current_year}-05-01', 'holiday_type': 'national'},
            {'name': 'Patriots Day', 'date': f'{current_year}-05-05', 'holiday_type': 'national'},
            {'name': 'Derg Downfall Day', 'date': f'{current_year}-05-28', 'holiday_type': 'national'},
        ]
        
        for holiday_data in holidays:
            holiday, created = Holiday.objects.get_or_create(
                name=holiday_data['name'],
                date=holiday_data['date'],
                defaults={
                    'holiday_type': holiday_data['holiday_type'],
                    'description': f'{holiday_data["name"]} - Ethiopian holiday'
                }
            )
            if created:
                self.stdout.write(f'Created holiday: {holiday.name}')

    def create_employees(self):
        # Create sample employees
        employees_data = [
            {
                'username': 'john.doe',
                'email': '<EMAIL>',
                'first_name': 'John',
                'last_name': 'Doe',
                'employee_id': 'EEU001',
                'department_code': 'IT',
                'position_code': 'IT-DEV',
                'phone_number': '+251911123456'
            },
            {
                'username': 'jane.smith',
                'email': '<EMAIL>',
                'first_name': 'Jane',
                'last_name': 'Smith',
                'employee_id': 'EEU002',
                'department_code': 'HR',
                'position_code': 'HR-OFF',
                'phone_number': '+251911234567'
            },
            {
                'username': 'mike.johnson',
                'email': '<EMAIL>',
                'first_name': 'Mike',
                'last_name': 'Johnson',
                'employee_id': 'EEU003',
                'department_code': 'FIN',
                'position_code': 'FIN-ACC',
                'phone_number': '+251911345678'
            },
        ]
        
        for emp_data in employees_data:
            # Create user
            user, created = User.objects.get_or_create(
                username=emp_data['username'],
                defaults={
                    'email': emp_data['email'],
                    'first_name': emp_data['first_name'],
                    'last_name': emp_data['last_name'],
                    'phone_number': emp_data['phone_number'],
                    'employee_id': emp_data['employee_id'],
                }
            )
            
            if created:
                user.set_password('password123')
                user.save()
                self.stdout.write(f'Created user: {user.username}')
                
                # Create employee
                department = Department.objects.get(code=emp_data['department_code'])
                position = Position.objects.get(code=emp_data['position_code'])
                
                employee = Employee.objects.create(
                    user=user,
                    employee_id=emp_data['employee_id'],
                    department=department,
                    position=position,
                    hire_date=timezone.now().date() - timedelta(days=365),
                    employment_type='permanent',
                    employment_status='active',
                    salary=50000.00
                )
                self.stdout.write(f'Created employee: {employee.employee_id}')

    def create_leave_balances(self):
        current_year = timezone.now().year
        employees = Employee.objects.all()
        leave_types = LeaveType.objects.all()
        
        for employee in employees:
            for leave_type in leave_types:
                balance, created = LeaveBalance.objects.get_or_create(
                    employee=employee,
                    leave_type=leave_type,
                    year=current_year,
                    defaults={
                        'allocated_days': leave_type.max_days_per_year,
                        'used_days': 0,
                    }
                )
                if created:
                    self.stdout.write(f'Created leave balance for {employee.employee_id} - {leave_type.name}')

    def create_attendance_records(self):
        employees = Employee.objects.all()
        
        # Create attendance records for the last 30 days
        for i in range(30):
            date = timezone.now().date() - timedelta(days=i)
            
            # Skip weekends
            if date.weekday() >= 5:
                continue
                
            for employee in employees:
                # 90% chance of attendance
                if i % 10 != 0:  # Skip some days to simulate absences
                    check_in_time = timezone.now().replace(
                        year=date.year,
                        month=date.month,
                        day=date.day,
                        hour=8,
                        minute=30,
                        second=0,
                        microsecond=0
                    ) + timedelta(minutes=(i % 60))  # Vary check-in time
                    
                    check_out_time = check_in_time + timedelta(hours=8, minutes=30)
                    
                    attendance, created = AttendanceRecord.objects.get_or_create(
                        employee=employee,
                        date=date,
                        defaults={
                            'check_in_time': check_in_time,
                            'check_out_time': check_out_time,
                            'status': 'present' if check_in_time.hour <= 8 else 'late',
                        }
                    )
                    
                    if created:
                        self.stdout.write(f'Created attendance for {employee.employee_id} on {date}')
