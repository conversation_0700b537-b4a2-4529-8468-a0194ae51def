#!/usr/bin/env python3
"""
Setup script for Ethiopian Electric Utility Attendance Management System
This script helps set up the project with MySQL database configuration.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header():
    print("=" * 70)
    print("Ethiopian Electric Utility - Attendance Management System")
    print("Setup Script")
    print("=" * 70)

def check_python_version():
    """Check if Python version is 3.8 or higher"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version.split()[0]}")

def check_mysql():
    """Check if MySQL is installed and accessible"""
    try:
        result = subprocess.run(['mysql', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ MySQL found: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ MySQL not found or not accessible")
        print("Please install MySQL and ensure it's in your PATH")
        return False

def create_virtual_environment():
    """Create virtual environment if it doesn't exist"""
    venv_path = Path("venv")
    if not venv_path.exists():
        print("📦 Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created")
    else:
        print("✅ Virtual environment already exists")

def get_pip_command():
    """Get the correct pip command for the platform"""
    if platform.system() == "Windows":
        return str(Path("venv") / "Scripts" / "pip.exe")
    else:
        return str(Path("venv") / "bin" / "pip")

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    pip_cmd = get_pip_command()
    
    # Upgrade pip first
    subprocess.run([pip_cmd, "install", "--upgrade", "pip"], check=True)
    
    # Install dependencies
    subprocess.run([pip_cmd, "install", "-r", "requirements.txt"], check=True)
    print("✅ Dependencies installed")

def create_env_file():
    """Create .env file from template"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file...")
        
        # Get database configuration from user
        print("\n🔧 Database Configuration:")
        db_name = input("Database name [eeu_attendance_db]: ").strip() or "eeu_attendance_db"
        db_user = input("Database user [eeu_user]: ").strip() or "eeu_user"
        db_password = input("Database password: ").strip()
        db_host = input("Database host [localhost]: ").strip() or "localhost"
        db_port = input("Database port [3306]: ").strip() or "3306"
        
        # Read template and replace values
        with open(env_example, 'r') as f:
            content = f.read()
        
        content = content.replace('eeu_attendance_db', db_name)
        content = content.replace('eeu_user', db_user)
        content = content.replace('your_secure_password_here', db_password)
        content = content.replace('localhost', db_host)
        content = content.replace('3306', db_port)
        
        # Generate secret key
        import secrets
        secret_key = secrets.token_urlsafe(50)
        content = content.replace('your-very-long-secret-key-here-change-this-in-production', secret_key)
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("✅ .env file created")
    else:
        print("✅ .env file already exists")

def get_python_command():
    """Get the correct Python command for the platform"""
    if platform.system() == "Windows":
        return str(Path("venv") / "Scripts" / "python.exe")
    else:
        return str(Path("venv") / "bin" / "python")

def run_migrations():
    """Run Django migrations"""
    print("🔄 Running database migrations...")
    python_cmd = get_python_command()
    
    # Make migrations
    subprocess.run([python_cmd, "manage.py", "makemigrations"], check=True)
    subprocess.run([python_cmd, "manage.py", "makemigrations", "accounts"], check=True)
    subprocess.run([python_cmd, "manage.py", "makemigrations", "employees"], check=True)
    subprocess.run([python_cmd, "manage.py", "makemigrations", "attendance"], check=True)
    subprocess.run([python_cmd, "manage.py", "makemigrations", "leaves"], check=True)
    
    # Apply migrations
    subprocess.run([python_cmd, "manage.py", "migrate"], check=True)
    print("✅ Migrations completed")

def create_superuser():
    """Create Django superuser"""
    print("👤 Creating superuser...")
    python_cmd = get_python_command()
    
    try:
        subprocess.run([python_cmd, "manage.py", "createsuperuser"], check=True)
        print("✅ Superuser created")
    except subprocess.CalledProcessError:
        print("⚠️  Superuser creation skipped or failed")

def create_sample_data():
    """Create sample data"""
    print("📊 Creating sample data...")
    python_cmd = get_python_command()
    
    try:
        subprocess.run([python_cmd, "manage.py", "create_sample_data"], check=True)
        print("✅ Sample data created")
    except subprocess.CalledProcessError:
        print("⚠️  Sample data creation failed")

def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'media', 'static', 'staticfiles']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✅ Directories created")

def print_mysql_setup_instructions():
    """Print MySQL setup instructions"""
    print("\n" + "=" * 70)
    print("📋 MySQL Setup Instructions")
    print("=" * 70)
    print("""
1. Connect to MySQL as root:
   mysql -u root -p

2. Create database and user:
   CREATE DATABASE eeu_attendance_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'eeu_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON eeu_attendance_db.* TO 'eeu_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;

3. Update the .env file with your database credentials.
""")

def print_completion_message():
    """Print completion message"""
    print("\n" + "=" * 70)
    print("🎉 Setup Complete!")
    print("=" * 70)
    print("""
To start the development server:

Windows:
  venv\\Scripts\\activate
  python manage.py runserver

macOS/Linux:
  source venv/bin/activate
  python manage.py runserver

Then visit: http://127.0.0.1:8000

Default login credentials:
- Admin: <EMAIL> / admin123
- HR: <EMAIL> / hr123
- Employee: <EMAIL> / emp123

⚠️  Remember to change default passwords in production!
""")

def main():
    """Main setup function"""
    print_header()
    
    # Check requirements
    check_python_version()
    
    if not check_mysql():
        print_mysql_setup_instructions()
        response = input("\nHave you set up MySQL? (y/n): ").lower()
        if response != 'y':
            print("Please set up MySQL first and run this script again.")
            sys.exit(1)
    
    # Setup steps
    create_virtual_environment()
    install_dependencies()
    create_directories()
    create_env_file()
    
    # Django setup
    try:
        run_migrations()
        create_superuser()
        create_sample_data()
        print_completion_message()
    except subprocess.CalledProcessError as e:
        print(f"❌ Setup failed: {e}")
        print("Please check the error messages above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
