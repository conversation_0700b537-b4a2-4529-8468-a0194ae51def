# MySQL Database Setup Guide
## Ethiopian Electric Utility - Attendance Management System

This guide provides detailed instructions for setting up MySQL database for the EEU Attendance Management System.

## Prerequisites

- MySQL 8.0 or higher
- Python 3.8 or higher
- Administrative access to MySQL server

## MySQL Installation

### Windows

1. **Download MySQL Installer**
   - Visit [MySQL Downloads](https://dev.mysql.com/downloads/installer/)
   - Download MySQL Installer for Windows
   - Choose "mysql-installer-community" version

2. **Install MySQL**
   ```
   - Run the installer as Administrator
   - Choose "Developer Default" setup type
   - Follow installation wizard
   - Set root password (remember this!)
   - Configure MySQL as Windows Service
   - Complete installation
   ```

3. **Verify Installation**
   ```cmd
   mysql --version
   ```

### Ubuntu/Debian

```bash
# Update package index
sudo apt update

# Install MySQL Server
sudo apt install mysql-server

# Secure MySQL installation
sudo mysql_secure_installation

# Start MySQL service
sudo systemctl start mysql
sudo systemctl enable mysql
```

### macOS

```bash
# Using Homebrew
brew install mysql

# Start MySQL service
brew services start mysql

# Secure installation
mysql_secure_installation
```

## Database Configuration

### 1. Connect to MySQL

```bash
mysql -u root -p
```
Enter the root password when prompted.

### 2. Create Database

```sql
-- Create database with UTF-8 support for Ethiopian characters
CREATE DATABASE eeu_attendance_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### 3. Create Database User

```sql
-- Create dedicated user for the application
CREATE USER 'eeu_user'@'localhost' IDENTIFIED BY 'SecurePassword123!';

-- Grant privileges
GRANT ALL PRIVILEGES ON eeu_attendance_db.* TO 'eeu_user'@'localhost';

-- Apply changes
FLUSH PRIVILEGES;
```

### 4. Verify Database Creation

```sql
-- Show databases
SHOW DATABASES;

-- Show user privileges
SHOW GRANTS FOR 'eeu_user'@'localhost';

-- Exit MySQL
EXIT;
```

### 5. Test Connection

```bash
mysql -u eeu_user -p eeu_attendance_db
```

## Django MySQL Configuration

### 1. Install MySQL Client

**Windows:**
```cmd
# Install Microsoft C++ Build Tools first if needed
pip install mysqlclient
```

**Ubuntu/Debian:**
```bash
sudo apt-get install python3-dev default-libmysqlclient-dev build-essential
pip install mysqlclient
```

**macOS:**
```bash
brew install mysql-client
export PATH="/usr/local/opt/mysql-client/bin:$PATH"
pip install mysqlclient
```

### 2. Alternative: PyMySQL (if mysqlclient fails)

```bash
pip install PyMySQL
```

Add to Django settings.py:
```python
import pymysql
pymysql.install_as_MySQLdb()
```

### 3. Environment Configuration

Create `.env` file:
```env
# Database Configuration
DB_NAME=eeu_attendance_db
DB_USER=eeu_user
DB_PASSWORD=SecurePassword123!
DB_HOST=localhost
DB_PORT=3306

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
```

### 4. Django Settings

The system is pre-configured with MySQL settings in `settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ.get('DB_NAME', 'eeu_attendance_db'),
        'USER': os.environ.get('DB_USER', 'root'),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '3306'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
    }
}
```

## Ethiopian Timezone Configuration

The system is configured for Ethiopian timezone:

```python
TIME_ZONE = 'Africa/Addis_Ababa'
USE_TZ = True
```

This ensures all timestamps are stored and displayed in Ethiopian time (EAT - UTC+3).

## Database Migration

After MySQL setup, run Django migrations:

```bash
# Activate virtual environment
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Create and apply migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Load sample data
python manage.py create_sample_data
```

## Performance Optimization

### 1. MySQL Configuration

Add to MySQL configuration file (`my.cnf` or `my.ini`):

```ini
[mysqld]
# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Performance settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
max_connections = 200

# Query cache
query_cache_type = 1
query_cache_size = 32M
```

### 2. Database Indexing

The Django models include appropriate indexes for:
- Foreign keys (automatic)
- Unique fields (automatic)
- Frequently queried fields

### 3. Connection Pooling

For production, consider using connection pooling:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        # ... other settings ...
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
            'autocommit': True,
        },
        'CONN_MAX_AGE': 600,  # Connection pooling
    }
}
```

## Backup and Maintenance

### 1. Database Backup

```bash
# Create backup
mysqldump -u eeu_user -p eeu_attendance_db > backup_$(date +%Y%m%d).sql

# Restore backup
mysql -u eeu_user -p eeu_attendance_db < backup_20240101.sql
```

### 2. Automated Backup Script

```bash
#!/bin/bash
# backup_db.sh
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u eeu_user -p eeu_attendance_db > "$BACKUP_DIR/eeu_backup_$DATE.sql"
```

### 3. Regular Maintenance

```sql
-- Optimize tables
OPTIMIZE TABLE attendance_attendancerecord;
OPTIMIZE TABLE leaves_leaverequest;

-- Check table status
SHOW TABLE STATUS;

-- Analyze tables
ANALYZE TABLE attendance_attendancerecord;
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   Error: Can't connect to MySQL server
   ```
   - Check if MySQL service is running
   - Verify host and port settings
   - Check firewall settings

2. **Authentication Failed**
   ```
   Error: Access denied for user
   ```
   - Verify username and password
   - Check user privileges
   - Ensure user can connect from specified host

3. **Character Encoding Issues**
   ```
   Error: Incorrect string value
   ```
   - Ensure database uses utf8mb4 charset
   - Check Django settings for proper encoding
   - Verify MySQL configuration

4. **mysqlclient Installation Error**
   ```
   Error: Microsoft Visual C++ 14.0 is required
   ```
   - Install Microsoft C++ Build Tools
   - Or use PyMySQL as alternative

### Debug Commands

```bash
# Check MySQL status
sudo systemctl status mysql

# Check MySQL error log
sudo tail -f /var/log/mysql/error.log

# Test Django database connection
python manage.py dbshell

# Check Django database settings
python manage.py check --database default
```

## Security Considerations

1. **Strong Passwords**
   - Use complex passwords for database users
   - Regularly rotate passwords

2. **User Privileges**
   - Grant minimum required privileges
   - Use separate users for different environments

3. **Network Security**
   - Restrict MySQL access to localhost in development
   - Use SSL connections in production

4. **Regular Updates**
   - Keep MySQL server updated
   - Monitor security advisories

## Production Deployment

For production deployment:

1. **Use SSL Connections**
2. **Configure proper backup strategy**
3. **Set up monitoring and alerting**
4. **Use read replicas for scaling**
5. **Implement proper logging**

---

**Note:** This guide is specifically tailored for the Ethiopian Electric Utility Attendance Management System. Adjust configurations based on your specific requirements and environment.
