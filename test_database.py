#!/usr/bin/env python3
"""
Test database connection for Django EEU Attendance System
This script verifies the database setup and shows current configuration.
"""

import os
import sys
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eeu_attendance.settings')

try:
    import django
    django.setup()
    
    from django.db import connection
    from django.conf import settings
    from django.contrib.auth.models import User
    
    def test_database_connection():
        """Test basic database connection"""
        print("🔍 Testing database connection...")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
            if result and result[0] == 1:
                print("✅ Database connection successful!")
                return True
            else:
                print("❌ Database connection failed!")
                return False
                
        except Exception as e:
            print(f"❌ Database connection error: {e}")
            return False
    
    def show_database_info():
        """Show database configuration information"""
        print("\n📊 Database Configuration:")
        print("-" * 40)
        
        db_config = settings.DATABASES['default']
        print(f"Engine: {db_config['ENGINE']}")
        print(f"Name: {db_config['NAME']}")
        
        if 'USER' in db_config:
            print(f"User: {db_config.get('USER', 'N/A')}")
            print(f"Host: {db_config.get('HOST', 'N/A')}")
            print(f"Port: {db_config.get('PORT', 'N/A')}")
    
    def show_tables():
        """Show database tables"""
        print("\n📋 Database Tables:")
        print("-" * 20)
        
        try:
            with connection.cursor() as cursor:
                # Get table names (works for both SQLite and MySQL)
                if 'sqlite' in settings.DATABASES['default']['ENGINE']:
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                else:
                    cursor.execute("SHOW TABLES")
                
                tables = cursor.fetchall()
                
                if tables:
                    print(f"Found {len(tables)} tables:")
                    for table in tables:
                        print(f"  ✓ {table[0]}")
                else:
                    print("No tables found")
                    
        except Exception as e:
            print(f"❌ Error getting tables: {e}")
    
    def show_users():
        """Show admin users"""
        print("\n👥 Admin Users:")
        print("-" * 15)
        
        try:
            users = User.objects.filter(is_superuser=True)
            if users:
                for user in users:
                    print(f"  👤 {user.username} ({user.email})")
            else:
                print("  No admin users found")
                
        except Exception as e:
            print(f"❌ Error getting users: {e}")
    
    def main():
        """Main test function"""
        print("=" * 60)
        print("🧪 Django Database Connection Test")
        print("Ethiopian Electric Utility - Attendance Management System")
        print("=" * 60)
        
        # Test connection
        if test_database_connection():
            show_database_info()
            show_tables()
            show_users()
            
            print("\n" + "=" * 60)
            print("✅ All tests passed! Database is ready for use.")
            print("\n🚀 Next steps:")
            print("1. Run: python manage.py runserver")
            print("2. Visit: http://127.0.0.1:8000/")
            print("3. Admin: http://127.0.0.1:8000/admin/")
            print("=" * 60)
        else:
            print("\n❌ Database tests failed!")
            print("Please check your database configuration.")
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\nMake sure you have:")
    print("1. Activated virtual environment")
    print("2. Installed requirements: pip install -r requirements.txt")
    print("3. Run migrations: python manage.py migrate")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
