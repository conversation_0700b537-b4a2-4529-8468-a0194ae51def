from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Avg
from datetime import datetime, date, timedelta
from .models import AttendanceRecord, AttendanceBreak, Holiday, AttendancePolicy
from employees.models import Employee


@login_required
def clock_in_out_view(request):
    """Clock in/out interface"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, 'Employee profile not found. Please contact HR.')
        return redirect('home')
    
    today = timezone.now().date()
    attendance_record, created = AttendanceRecord.objects.get_or_create(
        employee=employee,
        date=today,
        defaults={'status': 'absent'}
    )
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'clock_in' and not attendance_record.check_in_time:
            attendance_record.check_in_time = timezone.now()
            attendance_record.status = 'present'
            attendance_record.save()
            messages.success(request, f'Clocked in successfully at {attendance_record.check_in_time.strftime("%H:%M")}')
            
        elif action == 'clock_out' and attendance_record.check_in_time and not attendance_record.check_out_time:
            attendance_record.check_out_time = timezone.now()
            attendance_record.save()  # This will trigger the save method to calculate hours
            messages.success(request, f'Clocked out successfully at {attendance_record.check_out_time.strftime("%H:%M")}')
            
        elif action == 'break_start':
            if not AttendanceBreak.objects.filter(attendance_record=attendance_record, end_time__isnull=True).exists():
                AttendanceBreak.objects.create(
                    attendance_record=attendance_record,
                    break_type=request.POST.get('break_type', 'lunch'),
                    start_time=timezone.now()
                )
                messages.success(request, 'Break started')
            else:
                messages.warning(request, 'You already have an active break')
                
        elif action == 'break_end':
            active_break = AttendanceBreak.objects.filter(
                attendance_record=attendance_record, 
                end_time__isnull=True
            ).first()
            if active_break:
                active_break.end_time = timezone.now()
                active_break.save()
                messages.success(request, f'Break ended. Duration: {active_break.duration_minutes} minutes')
            else:
                messages.warning(request, 'No active break found')
        
        return redirect('attendance:clock')
    
    # Get active break
    active_break = AttendanceBreak.objects.filter(
        attendance_record=attendance_record, 
        end_time__isnull=True
    ).first()
    
    context = {
        'attendance_record': attendance_record,
        'active_break': active_break,
        'current_time': timezone.now(),
    }
    return render(request, 'attendance/clock.html', context)


@login_required
def my_attendance_view(request):
    """Employee's attendance history"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, 'Employee profile not found. Please contact HR.')
        return redirect('home')
    
    # Get filter parameters
    month = request.GET.get('month', timezone.now().month)
    year = request.GET.get('year', timezone.now().year)
    
    try:
        month = int(month)
        year = int(year)
    except (ValueError, TypeError):
        month = timezone.now().month
        year = timezone.now().year
    
    # Get attendance records for the month
    attendance_records = AttendanceRecord.objects.filter(
        employee=employee,
        date__year=year,
        date__month=month
    ).order_by('-date')
    
    # Calculate statistics
    total_days = attendance_records.count()
    present_days = attendance_records.filter(status='present').count()
    late_days = attendance_records.filter(status='late').count()
    absent_days = attendance_records.filter(status='absent').count()
    total_hours = attendance_records.aggregate(Sum('total_hours'))['total_hours__sum'] or 0
    total_overtime = attendance_records.aggregate(Sum('overtime_hours'))['overtime_hours__sum'] or 0
    
    # Pagination
    paginator = Paginator(attendance_records, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'month': month,
        'year': year,
        'total_days': total_days,
        'present_days': present_days,
        'late_days': late_days,
        'absent_days': absent_days,
        'total_hours': total_hours,
        'total_overtime': total_overtime,
        'attendance_rate': round((present_days / total_days * 100) if total_days > 0 else 0, 1),
    }
    return render(request, 'attendance/my_attendance.html', context)


@login_required
def attendance_detail_view(request, record_id):
    """Detailed view of a specific attendance record"""
    try:
        employee = request.user.employee
    except Employee.DoesNotExist:
        messages.error(request, 'Employee profile not found.')
        return redirect('home')
    
    attendance_record = get_object_or_404(
        AttendanceRecord, 
        id=record_id, 
        employee=employee
    )
    
    breaks = AttendanceBreak.objects.filter(attendance_record=attendance_record)
    
    context = {
        'attendance_record': attendance_record,
        'breaks': breaks,
    }
    return render(request, 'attendance/detail.html', context)


@login_required
def all_attendance_view(request):
    """HR view of all employee attendance (requires staff permission)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to view this page.')
        return redirect('home')
    
    # Get filter parameters
    search = request.GET.get('search', '')
    department = request.GET.get('department', '')
    status = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    
    # Base queryset
    attendance_records = AttendanceRecord.objects.select_related(
        'employee__user', 'employee__department'
    ).order_by('-date', 'employee__employee_id')
    
    # Apply filters
    if search:
        attendance_records = attendance_records.filter(
            Q(employee__employee_id__icontains=search) |
            Q(employee__user__first_name__icontains=search) |
            Q(employee__user__last_name__icontains=search)
        )
    
    if department:
        attendance_records = attendance_records.filter(employee__department_id=department)
    
    if status:
        attendance_records = attendance_records.filter(status=status)
    
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            attendance_records = attendance_records.filter(date__gte=date_from)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            attendance_records = attendance_records.filter(date__lte=date_to)
        except ValueError:
            pass
    
    # Pagination
    paginator = Paginator(attendance_records, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get departments for filter dropdown
    from employees.models import Department
    departments = Department.objects.filter(is_active=True)
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'search': search,
        'department': department,
        'status': status,
        'date_from': date_from,
        'date_to': date_to,
        'status_choices': AttendanceRecord.STATUS_CHOICES,
    }
    return render(request, 'attendance/all_attendance.html', context)


@login_required
def attendance_api_status(request):
    """API endpoint for current attendance status"""
    try:
        employee = request.user.employee
        today = timezone.now().date()
        attendance_record = AttendanceRecord.objects.filter(
            employee=employee, date=today
        ).first()
        
        if attendance_record:
            data = {
                'clocked_in': bool(attendance_record.check_in_time),
                'clocked_out': bool(attendance_record.check_out_time),
                'check_in_time': attendance_record.check_in_time.strftime('%H:%M') if attendance_record.check_in_time else None,
                'check_out_time': attendance_record.check_out_time.strftime('%H:%M') if attendance_record.check_out_time else None,
                'total_hours': float(attendance_record.total_hours),
                'status': attendance_record.status,
            }
        else:
            data = {
                'clocked_in': False,
                'clocked_out': False,
                'check_in_time': None,
                'check_out_time': None,
                'total_hours': 0,
                'status': 'absent',
            }
        
        return JsonResponse(data)
    except Employee.DoesNotExist:
        return JsonResponse({'error': 'Employee profile not found'}, status=404)
