{% extends 'base/base.html' %}
{% load static %}

{% block title %}Request Leave - EEU Attendance{% endblock %}

{% block extra_css %}
<style>
    .leave-form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .balance-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .balance-item {
        text-align: center;
        padding: 1rem;
        border-right: 1px solid #dee2e6;
    }

    .balance-item:last-child {
        border-right: none;
    }

    .balance-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1e3c72;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
    }

    .date-info {
        background: #e3f2fd;
        border-radius: 5px;
        padding: 0.5rem;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container leave-form-container">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-calendar-plus"></i> Request Leave</h2>
            <p class="text-muted">Submit a new leave request</p>
        </div>
    </div>

    <!-- Leave Balances -->
    <div class="card balance-card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Your Leave Balances</h5>
        </div>
        <div class="card-body p-0">
            <div class="row no-gutters">
                {% for balance in leave_balances %}
                <div class="col-md-3 balance-item">
                    <div class="balance-number">{{ balance.available_days }}</div>
                    <div class="text-muted">{{ balance.leave_type.name }}</div>
                    <small class="text-muted">of {{ balance.allocated_days }} days</small>
                </div>
                {% empty %}
                <div class="col-12 text-center py-3">
                    <p class="text-muted mb-0">No leave balances available</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Leave Request Form -->
    <div class="form-section">
        <h4 class="mb-4"><i class="fas fa-edit"></i> Leave Request Details</h4>

        <form method="post" id="leaveRequestForm">
            {% csrf_token %}

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="leave_type" class="form-label required-field">Leave Type</label>
                        <select name="leave_type" id="leave_type" class="form-select" required>
                            <option value="">Select leave type...</option>
                            {% for leave_type in leave_types %}
                            <option value="{{ leave_type.id }}"
                                    data-max-days="{{ leave_type.max_days_per_year }}"
                                    data-notice-days="{{ leave_type.min_notice_days }}"
                                    data-description="{{ leave_type.description }}">
                                {{ leave_type.name }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text" id="leaveTypeInfo"></div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="duration_type" class="form-label required-field">Duration Type</label>
                        <select name="duration_type" id="duration_type" class="form-select" required>
                            <option value="full_day">Full Day</option>
                            <option value="half_day_morning">Half Day (Morning)</option>
                            <option value="half_day_afternoon">Half Day (Afternoon)</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="start_date" class="form-label required-field">Start Date</label>
                        <input type="date" name="start_date" id="start_date" class="form-control" required>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="end_date" class="form-label required-field">End Date</label>
                        <input type="date" name="end_date" id="end_date" class="form-control" required>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="reason" class="form-label required-field">Reason for Leave</label>
                <textarea name="reason" id="reason" class="form-control" rows="4"
                          placeholder="Please provide a detailed reason for your leave request..." required></textarea>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emergency_contact" class="form-label">Emergency Contact Name</label>
                        <input type="text" name="emergency_contact" id="emergency_contact" class="form-control"
                               placeholder="Contact person during leave">
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emergency_phone" class="form-label">Emergency Contact Phone</label>
                        <input type="tel" name="emergency_phone" id="emergency_phone" class="form-control"
                               placeholder="+251-xxx-xxx-xxx">
                    </div>
                </div>
            </div>

            <!-- Leave Summary -->
            <div class="alert alert-info" id="leaveSummary" style="display: none;">
                <h6><i class="fas fa-info-circle"></i> Leave Summary</h6>
                <div id="summaryContent"></div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{% url 'leaves:my_leaves' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to My Leaves
                </a>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Submit Request
                </button>
            </div>
        </form>
    </div>

    <!-- Guidelines -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-info-circle"></i> Leave Request Guidelines</h5>
        </div>
        <div class="card-body">
            <ul class="mb-0">
                <li>All leave requests must be submitted in advance according to company policy</li>
                <li>Emergency leaves may be submitted with shorter notice but require approval</li>
                <li>Annual leave requests should be planned and submitted early</li>
                <li>Medical leave may require supporting documentation</li>
                <li>Contact your supervisor for urgent leave requirements</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const leaveTypeSelect = document.getElementById('leave_type');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const durationTypeSelect = document.getElementById('duration_type');
    const leaveSummary = document.getElementById('leaveSummary');
    const summaryContent = document.getElementById('summaryContent');

    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    startDateInput.min = today;
    endDateInput.min = today;

    // Update leave type info
    leaveTypeSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const infoDiv = document.getElementById('leaveTypeInfo');

        if (selectedOption.value) {
            const maxDays = selectedOption.dataset.maxDays;
            const noticeDays = selectedOption.dataset.noticeDays;
            const description = selectedOption.dataset.description;

            infoDiv.innerHTML = `
                <small class="text-info">
                    <strong>Max days per year:</strong> ${maxDays} |
                    <strong>Notice required:</strong> ${noticeDays} days<br>
                    ${description}
                </small>
            `;
        } else {
            infoDiv.innerHTML = '';
        }
        updateSummary();
    });

    // Update end date minimum when start date changes
    startDateInput.addEventListener('change', function() {
        endDateInput.min = this.value;
        if (endDateInput.value && endDateInput.value < this.value) {
            endDateInput.value = this.value;
        }
        updateSummary();
    });

    endDateInput.addEventListener('change', updateSummary);
    durationTypeSelect.addEventListener('change', updateSummary);

    function updateSummary() {
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        const durationType = durationTypeSelect.value;

        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const timeDiff = end.getTime() - start.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;

            let totalDays = daysDiff;
            if (durationType.includes('half_day') && daysDiff === 1) {
                totalDays = 0.5;
            }

            summaryContent.innerHTML = `
                <strong>Duration:</strong> ${totalDays} day(s)<br>
                <strong>From:</strong> ${start.toLocaleDateString()}
                <strong>To:</strong> ${end.toLocaleDateString()}<br>
                <strong>Type:</strong> ${durationType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            `;
            leaveSummary.style.display = 'block';
        } else {
            leaveSummary.style.display = 'none';
        }
    }
});
</script>
{% endblock %}
