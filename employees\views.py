from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from .models import Employee, Department, Position, WorkSchedule
from accounts.models import User


@login_required
def employee_list_view(request):
    """List all employees (HR view)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to view this page.')
        return redirect('home')
    
    # Get filter parameters
    search = request.GET.get('search', '')
    department = request.GET.get('department', '')
    status = request.GET.get('status', '')
    
    # Base queryset
    employees = Employee.objects.select_related(
        'user', 'department', 'position', 'supervisor'
    ).order_by('employee_id')
    
    # Apply filters
    if search:
        employees = employees.filter(
            Q(employee_id__icontains=search) |
            Q(user__first_name__icontains=search) |
            Q(user__last_name__icontains=search) |
            Q(user__email__icontains=search)
        )
    
    if department:
        employees = employees.filter(department_id=department)
    
    if status:
        employees = employees.filter(employment_status=status)
    
    # Pagination
    paginator = Paginator(employees, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    departments = Department.objects.filter(is_active=True)
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'search': search,
        'department': department,
        'status': status,
        'status_choices': Employee.EMPLOYMENT_STATUS_CHOICES,
    }
    return render(request, 'employees/list.html', context)


@login_required
def employee_detail_view(request, employee_id):
    """Detailed view of an employee"""
    if not request.user.is_staff:
        # Allow employees to view their own profile
        try:
            if request.user.employee.employee_id != employee_id:
                messages.error(request, 'You do not have permission to view this page.')
                return redirect('home')
        except Employee.DoesNotExist:
            messages.error(request, 'Employee profile not found.')
            return redirect('home')
    
    employee = get_object_or_404(Employee, employee_id=employee_id)
    work_schedules = WorkSchedule.objects.filter(employee=employee, effective_to__isnull=True)
    
    # Get recent attendance (last 10 records)
    from attendance.models import AttendanceRecord
    recent_attendance = AttendanceRecord.objects.filter(
        employee=employee
    ).order_by('-date')[:10]
    
    # Get recent leave requests (last 5)
    from leaves.models import LeaveRequest
    recent_leaves = LeaveRequest.objects.filter(
        employee=employee
    ).order_by('-applied_on')[:5]
    
    context = {
        'employee': employee,
        'work_schedules': work_schedules,
        'recent_attendance': recent_attendance,
        'recent_leaves': recent_leaves,
    }
    return render(request, 'employees/detail.html', context)


@login_required
def employee_create_view(request):
    """Create new employee (HR only)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to access this page.')
        return redirect('home')
    
    if request.method == 'POST':
        # Get form data
        username = request.POST.get('username')
        email = request.POST.get('email')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        employee_id = request.POST.get('employee_id')
        department_id = request.POST.get('department')
        position_id = request.POST.get('position')
        hire_date = request.POST.get('hire_date')
        employment_type = request.POST.get('employment_type', 'permanent')
        phone_number = request.POST.get('phone_number', '')
        
        try:
            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
                phone_number=phone_number,
                employee_id=employee_id,
                password='temp123'  # Temporary password
            )
            
            # Create employee
            department = Department.objects.get(id=department_id)
            position = Position.objects.get(id=position_id)
            
            employee = Employee.objects.create(
                user=user,
                employee_id=employee_id,
                department=department,
                position=position,
                hire_date=hire_date,
                employment_type=employment_type,
            )
            
            messages.success(request, f'Employee {employee_id} created successfully. Temporary password: temp123')
            return redirect('employees:detail', employee_id=employee_id)
            
        except Exception as e:
            messages.error(request, f'Error creating employee: {str(e)}')
    
    # Get form options
    departments = Department.objects.filter(is_active=True)
    positions = Position.objects.filter(is_active=True)
    
    context = {
        'departments': departments,
        'positions': positions,
        'employment_types': Employee.EMPLOYMENT_TYPE_CHOICES,
    }
    return render(request, 'employees/create.html', context)


@login_required
def employee_edit_view(request, employee_id):
    """Edit employee details (HR only)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to access this page.')
        return redirect('home')
    
    employee = get_object_or_404(Employee, employee_id=employee_id)
    
    if request.method == 'POST':
        # Update user fields
        employee.user.first_name = request.POST.get('first_name', employee.user.first_name)
        employee.user.last_name = request.POST.get('last_name', employee.user.last_name)
        employee.user.email = request.POST.get('email', employee.user.email)
        employee.user.phone_number = request.POST.get('phone_number', employee.user.phone_number)
        employee.user.save()
        
        # Update employee fields
        department_id = request.POST.get('department')
        position_id = request.POST.get('position')
        
        if department_id:
            employee.department = Department.objects.get(id=department_id)
        if position_id:
            employee.position = Position.objects.get(id=position_id)
        
        employee.employment_status = request.POST.get('employment_status', employee.employment_status)
        employee.employment_type = request.POST.get('employment_type', employee.employment_type)
        employee.salary = request.POST.get('salary') or employee.salary
        employee.save()
        
        messages.success(request, 'Employee updated successfully.')
        return redirect('employees:detail', employee_id=employee_id)
    
    # Get form options
    departments = Department.objects.filter(is_active=True)
    positions = Position.objects.filter(is_active=True)
    
    context = {
        'employee': employee,
        'departments': departments,
        'positions': positions,
        'employment_statuses': Employee.EMPLOYMENT_STATUS_CHOICES,
        'employment_types': Employee.EMPLOYMENT_TYPE_CHOICES,
    }
    return render(request, 'employees/edit.html', context)


@login_required
def department_list_view(request):
    """List all departments (HR view)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to view this page.')
        return redirect('home')
    
    departments = Department.objects.all().order_by('name')
    
    context = {
        'departments': departments,
    }
    return render(request, 'employees/departments.html', context)


@login_required
def position_list_view(request):
    """List all positions (HR view)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to view this page.')
        return redirect('home')
    
    positions = Position.objects.select_related('department').all().order_by('department', 'level', 'title')
    
    context = {
        'positions': positions,
    }
    return render(request, 'employees/positions.html', context)


@login_required
def get_positions_by_department(request):
    """API endpoint to get positions by department"""
    department_id = request.GET.get('department_id')
    if department_id:
        positions = Position.objects.filter(
            department_id=department_id, 
            is_active=True
        ).values('id', 'title')
        return JsonResponse({'positions': list(positions)})
    return JsonResponse({'positions': []})
