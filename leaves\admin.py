from django.contrib import admin
from .models import LeaveType, LeaveBalance, LeaveRequest, LeaveApprovalWorkflow, LeaveEncashment


@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'max_days_per_year', 'is_paid', 'requires_approval', 'is_active')
    list_filter = ('is_paid', 'requires_approval', 'is_active', 'is_gender_specific')
    search_fields = ('name', 'code', 'description')
    ordering = ('name',)


@admin.register(LeaveBalance)
class LeaveBalanceAdmin(admin.ModelAdmin):
    list_display = ('employee', 'leave_type', 'year', 'allocated_days', 'used_days', 'available_days')
    list_filter = ('leave_type', 'year')
    search_fields = ('employee__employee_id', 'employee__user__first_name', 'employee__user__last_name')
    ordering = ('employee', 'year', 'leave_type')


@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    list_display = ('employee', 'leave_type', 'start_date', 'end_date', 'total_days', 'status', 'applied_on')
    list_filter = ('leave_type', 'status', 'duration_type', 'applied_on')
    search_fields = ('employee__employee_id', 'employee__user__first_name', 'employee__user__last_name', 'reason')
    ordering = ('-applied_on',)
    readonly_fields = ('total_days', 'applied_on', 'approved_on')


@admin.register(LeaveApprovalWorkflow)
class LeaveApprovalWorkflowAdmin(admin.ModelAdmin):
    list_display = ('leave_request', 'approver', 'step_order', 'approved', 'approved_on')
    list_filter = ('approved', 'step_order', 'approved_on')
    search_fields = ('leave_request__employee__employee_id', 'approver__first_name', 'approver__last_name')
    ordering = ('leave_request', 'step_order')


@admin.register(LeaveEncashment)
class LeaveEncashmentAdmin(admin.ModelAdmin):
    list_display = ('employee', 'leave_type', 'year', 'days_to_encash', 'total_amount', 'status')
    list_filter = ('leave_type', 'status', 'year')
    search_fields = ('employee__employee_id', 'employee__user__first_name', 'employee__user__last_name')
    ordering = ('-requested_on',)
