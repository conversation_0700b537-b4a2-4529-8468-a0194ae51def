"""
URL configuration for eeu_attendance project.

Ethiopian Electric Utility Employee Attendance Management System
"""
from django.contrib import admin
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from django.http import HttpResponse

def home_view(request):
    """Simple home view"""
    return HttpResponse("""
    <h1>Ethiopian Electric Utility - Attendance Management System</h1>
    <p>Welcome to the EEU Attendance Management System</p>
    <p><a href="/admin/">Admin Panel</a></p>
    <p>System is being configured with MySQL database support.</p>
    """)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', home_view, name='home'),
    # Additional URLs will be added after creating the apps
    # path('accounts/', include('accounts.urls')),
    # path('employees/', include('employees.urls')),
    # path('attendance/', include('attendance.urls')),
    # path('leaves/', include('leaves.urls')),
    # path('reports/', include('reports.urls')),
    # path('dashboard/', include('dashboard.urls')),
    # path('api/', include('api.urls')),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
