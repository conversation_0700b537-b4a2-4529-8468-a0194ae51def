from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
import csv
from attendance.models import AttendanceRecord
from leaves.models import LeaveRequest
from employees.models import Employee, Department


@login_required
def reports_dashboard(request):
    """Reports dashboard (HR only)"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to view this page.')
        return redirect('home')
    
    # Get current month statistics
    current_month = timezone.now().replace(day=1)
    next_month = (current_month + timedelta(days=32)).replace(day=1)
    
    # Attendance statistics
    total_employees = Employee.objects.filter(employment_status='active').count()
    
    attendance_stats = AttendanceRecord.objects.filter(
        date__gte=current_month,
        date__lt=next_month
    ).aggregate(
        total_records=Count('id'),
        present_count=Count('id', filter=Q(status='present')),
        late_count=Count('id', filter=Q(status='late')),
        absent_count=Count('id', filter=Q(status='absent')),
        total_hours=Sum('total_hours'),
        total_overtime=Sum('overtime_hours')
    )
    
    # Leave statistics
    leave_stats = LeaveRequest.objects.filter(
        start_date__gte=current_month,
        start_date__lt=next_month
    ).aggregate(
        total_requests=Count('id'),
        pending_requests=Count('id', filter=Q(status='pending')),
        approved_requests=Count('id', filter=Q(status='approved')),
        rejected_requests=Count('id', filter=Q(status='rejected'))
    )
    
    # Department-wise attendance
    dept_attendance = AttendanceRecord.objects.filter(
        date__gte=current_month,
        date__lt=next_month
    ).values(
        'employee__department__name'
    ).annotate(
        total_records=Count('id'),
        present_count=Count('id', filter=Q(status='present'))
    ).order_by('employee__department__name')
    
    context = {
        'total_employees': total_employees,
        'attendance_stats': attendance_stats,
        'leave_stats': leave_stats,
        'dept_attendance': dept_attendance,
        'current_month': current_month,
    }
    return render(request, 'reports/dashboard.html', context)


@login_required
def attendance_report(request):
    """Detailed attendance report"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to view this page.')
        return redirect('home')
    
    # Get filter parameters
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    department = request.GET.get('department', '')
    employee_id = request.GET.get('employee_id', '')
    
    # Default to current month if no dates provided
    if not date_from:
        date_from = timezone.now().replace(day=1).date()
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Build query
    attendance_records = AttendanceRecord.objects.select_related(
        'employee__user', 'employee__department'
    ).filter(date__gte=date_from, date__lte=date_to)
    
    if department:
        attendance_records = attendance_records.filter(employee__department_id=department)
    
    if employee_id:
        attendance_records = attendance_records.filter(employee__employee_id__icontains=employee_id)
    
    attendance_records = attendance_records.order_by('-date', 'employee__employee_id')
    
    # Calculate summary statistics
    summary = attendance_records.aggregate(
        total_records=Count('id'),
        present_count=Count('id', filter=Q(status='present')),
        late_count=Count('id', filter=Q(status='late')),
        absent_count=Count('id', filter=Q(status='absent')),
        total_hours=Sum('total_hours'),
        avg_hours=Avg('total_hours'),
        total_overtime=Sum('overtime_hours')
    )
    
    # Get departments for filter
    departments = Department.objects.filter(is_active=True)
    
    context = {
        'attendance_records': attendance_records[:100],  # Limit for performance
        'summary': summary,
        'departments': departments,
        'date_from': date_from,
        'date_to': date_to,
        'department': department,
        'employee_id': employee_id,
    }
    return render(request, 'reports/attendance.html', context)


@login_required
def leave_report(request):
    """Detailed leave report"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to view this page.')
        return redirect('home')
    
    # Get filter parameters
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    department = request.GET.get('department', '')
    status = request.GET.get('status', '')
    
    # Default to current month if no dates provided
    if not date_from:
        date_from = timezone.now().replace(day=1).date()
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    
    if not date_to:
        date_to = timezone.now().date()
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Build query
    leave_requests = LeaveRequest.objects.select_related(
        'employee__user', 'employee__department', 'leave_type'
    ).filter(start_date__gte=date_from, start_date__lte=date_to)
    
    if department:
        leave_requests = leave_requests.filter(employee__department_id=department)
    
    if status:
        leave_requests = leave_requests.filter(status=status)
    
    leave_requests = leave_requests.order_by('-applied_on')
    
    # Calculate summary statistics
    summary = leave_requests.aggregate(
        total_requests=Count('id'),
        pending_requests=Count('id', filter=Q(status='pending')),
        approved_requests=Count('id', filter=Q(status='approved')),
        rejected_requests=Count('id', filter=Q(status='rejected')),
        total_days=Sum('total_days')
    )
    
    # Get departments for filter
    departments = Department.objects.filter(is_active=True)
    
    context = {
        'leave_requests': leave_requests[:100],  # Limit for performance
        'summary': summary,
        'departments': departments,
        'date_from': date_from,
        'date_to': date_to,
        'department': department,
        'status': status,
        'status_choices': LeaveRequest.STATUS_CHOICES,
    }
    return render(request, 'reports/leaves.html', context)


@login_required
def export_attendance_csv(request):
    """Export attendance data to CSV"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to perform this action.')
        return redirect('home')
    
    # Get same filters as attendance_report
    date_from = request.GET.get('date_from', timezone.now().replace(day=1).date())
    date_to = request.GET.get('date_to', timezone.now().date())
    department = request.GET.get('department', '')
    
    if isinstance(date_from, str):
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    if isinstance(date_to, str):
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Build query
    attendance_records = AttendanceRecord.objects.select_related(
        'employee__user', 'employee__department'
    ).filter(date__gte=date_from, date__lte=date_to)
    
    if department:
        attendance_records = attendance_records.filter(employee__department_id=department)
    
    attendance_records = attendance_records.order_by('-date', 'employee__employee_id')
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="attendance_report_{date_from}_{date_to}.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'Employee ID', 'Employee Name', 'Department', 'Date', 
        'Check In', 'Check Out', 'Total Hours', 'Overtime Hours', 'Status'
    ])
    
    for record in attendance_records:
        writer.writerow([
            record.employee.employee_id,
            record.employee.user.get_full_name(),
            record.employee.department.name,
            record.date,
            record.check_in_time.strftime('%H:%M') if record.check_in_time else '',
            record.check_out_time.strftime('%H:%M') if record.check_out_time else '',
            record.total_hours,
            record.overtime_hours,
            record.get_status_display()
        ])
    
    return response


@login_required
def export_leaves_csv(request):
    """Export leave data to CSV"""
    if not request.user.is_staff:
        messages.error(request, 'You do not have permission to perform this action.')
        return redirect('home')
    
    # Get same filters as leave_report
    date_from = request.GET.get('date_from', timezone.now().replace(day=1).date())
    date_to = request.GET.get('date_to', timezone.now().date())
    department = request.GET.get('department', '')
    status = request.GET.get('status', '')
    
    if isinstance(date_from, str):
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    if isinstance(date_to, str):
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Build query
    leave_requests = LeaveRequest.objects.select_related(
        'employee__user', 'employee__department', 'leave_type'
    ).filter(start_date__gte=date_from, start_date__lte=date_to)
    
    if department:
        leave_requests = leave_requests.filter(employee__department_id=department)
    
    if status:
        leave_requests = leave_requests.filter(status=status)
    
    leave_requests = leave_requests.order_by('-applied_on')
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="leave_report_{date_from}_{date_to}.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'Employee ID', 'Employee Name', 'Department', 'Leave Type',
        'Start Date', 'End Date', 'Total Days', 'Status', 'Applied On', 'Reason'
    ])
    
    for request_obj in leave_requests:
        writer.writerow([
            request_obj.employee.employee_id,
            request_obj.employee.user.get_full_name(),
            request_obj.employee.department.name,
            request_obj.leave_type.name,
            request_obj.start_date,
            request_obj.end_date,
            request_obj.total_days,
            request_obj.get_status_display(),
            request_obj.applied_on.date(),
            request_obj.reason[:100]  # Truncate long reasons
        ])
    
    return response
