// Ethiopian Electric Utility - Custom JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Add loading state to buttons on form submit
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<span class="loading"></span> Processing...';
                submitBtn.disabled = true;
            }
        });
    });

    // Real-time clock for dashboard
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Africa/Addis_Ababa'
        });
        
        const clockElement = document.getElementById('live-clock');
        if (clockElement) {
            clockElement.textContent = timeString;
        }
    }

    // Update clock every second
    setInterval(updateClock, 1000);
    updateClock(); // Initial call

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    });

    cards.forEach((card) => {
        observer.observe(card);
    });

    // Quick action button enhancements
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            // Add ripple effect
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Feature coming soon notifications
    window.showComingSoon = function(feature) {
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-info border-0';
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-info-circle"></i> ${feature || 'This feature'} is coming soon!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // Add toast container if it doesn't exist
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            toast.remove();
        });
    };

    // System status indicator
    function updateSystemStatus() {
        const statusElement = document.querySelector('.system-status');
        if (statusElement) {
            // Simulate system health check
            const isOnline = navigator.onLine;
            if (isOnline) {
                statusElement.className = 'system-status status-operational';
                statusElement.innerHTML = '<i class="fas fa-check-circle"></i> System Operational';
            } else {
                statusElement.className = 'system-status status-offline';
                statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> System Offline';
            }
        }
    }

    // Check system status every 30 seconds
    setInterval(updateSystemStatus, 30000);
    updateSystemStatus(); // Initial call

    // Add Ethiopian time zone display
    function displayEthiopianTime() {
        const timeElement = document.querySelector('.ethiopian-time');
        if (timeElement) {
            const now = new Date();
            const ethiopianTime = now.toLocaleString('en-US', {
                timeZone: 'Africa/Addis_Ababa',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = `Ethiopian Time: ${ethiopianTime}`;
        }
    }

    setInterval(displayEthiopianTime, 1000);
    displayEthiopianTime();

    // Enhanced form validation
    const forms_with_validation = document.querySelectorAll('.needs-validation');
    forms_with_validation.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl + H for Home
        if (e.ctrlKey && e.key === 'h') {
            e.preventDefault();
            window.location.href = '/';
        }
        
        // Ctrl + D for Dashboard
        if (e.ctrlKey && e.key === 'd') {
            e.preventDefault();
            if (document.querySelector('.navbar-nav a[href*="dashboard"]')) {
                window.location.href = '/';
            }
        }
        
        // Ctrl + L for Logout
        if (e.ctrlKey && e.key === 'l') {
            e.preventDefault();
            const logoutLink = document.querySelector('a[href*="logout"]');
            if (logoutLink) {
                logoutLink.click();
            }
        }
    });

    // Add ripple effect CSS if not already present
    if (!document.querySelector('#ripple-styles')) {
        const style = document.createElement('style');
        style.id = 'ripple-styles';
        style.textContent = `
            .ripple {
                position: absolute;
                border-radius: 50%;
                background-color: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }
            
            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    console.log('🚀 EEU Attendance Management System - JavaScript Loaded');
    console.log('⚡ Ethiopian Electric Utility - Ready for Action!');
});
