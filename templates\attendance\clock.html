{% extends 'base/base.html' %}
{% load static %}

{% block title %}Clock In/Out - EEU Attendance{% endblock %}

{% block extra_css %}
<style>
    .clock-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .time-display {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        color: #1e3c72;
        margin-bottom: 2rem;
        font-family: 'Courier New', monospace;
    }
    
    .status-card {
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .clock-btn {
        font-size: 1.5rem;
        padding: 15px 40px;
        border-radius: 50px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        min-width: 200px;
    }
    
    .clock-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }
    
    .break-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .status-indicator {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 10px;
    }
    
    .status-present { background-color: #28a745; }
    .status-absent { background-color: #dc3545; }
    .status-break { background-color: #ffc107; }
</style>
{% endblock %}

{% block content %}
<div class="container clock-container">
    <!-- Live Time Display -->
    <div class="time-display" id="live-time">
        {{ current_time|date:"H:i:s" }}
    </div>
    
    <!-- Current Status Card -->
    <div class="card status-card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="fas fa-clock"></i> Today's Attendance Status
            </h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Check In Time:</h6>
                    {% if attendance_record.check_in_time %}
                        <span class="status-indicator status-present"></span>
                        <strong>{{ attendance_record.check_in_time|date:"H:i" }}</strong>
                    {% else %}
                        <span class="status-indicator status-absent"></span>
                        <span class="text-muted">Not clocked in</span>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    <h6>Check Out Time:</h6>
                    {% if attendance_record.check_out_time %}
                        <span class="status-indicator status-present"></span>
                        <strong>{{ attendance_record.check_out_time|date:"H:i" }}</strong>
                    {% else %}
                        <span class="status-indicator status-absent"></span>
                        <span class="text-muted">Not clocked out</span>
                    {% endif %}
                </div>
            </div>
            
            {% if attendance_record.total_hours %}
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6>Total Hours:</h6>
                    <strong>{{ attendance_record.total_hours }} hours</strong>
                </div>
                <div class="col-md-6">
                    <h6>Status:</h6>
                    <span class="badge badge-{{ attendance_record.status|yesno:'success,warning,danger' }}">
                        {{ attendance_record.get_status_display }}
                    </span>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Clock In/Out Actions -->
    <div class="text-center mb-4">
        <form method="post" class="d-inline">
            {% csrf_token %}
            {% if not attendance_record.check_in_time %}
                <button type="submit" name="action" value="clock_in" class="btn btn-success clock-btn">
                    <i class="fas fa-sign-in-alt"></i> Clock In
                </button>
            {% elif not attendance_record.check_out_time %}
                <button type="submit" name="action" value="clock_out" class="btn btn-danger clock-btn">
                    <i class="fas fa-sign-out-alt"></i> Clock Out
                </button>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-check-circle"></i>
                    You have completed your work day. Total hours: {{ attendance_record.total_hours }}
                </div>
            {% endif %}
        </form>
    </div>
    
    <!-- Break Management -->
    {% if attendance_record.check_in_time and not attendance_record.check_out_time %}
    <div class="break-section">
        <h5><i class="fas fa-coffee"></i> Break Management</h5>
        
        {% if active_break %}
            <div class="alert alert-warning">
                <span class="status-indicator status-break"></span>
                <strong>On Break:</strong> {{ active_break.get_break_type_display }} 
                (Started at {{ active_break.start_time|date:"H:i" }})
            </div>
            
            <form method="post" class="d-inline">
                {% csrf_token %}
                <button type="submit" name="action" value="break_end" class="btn btn-warning">
                    <i class="fas fa-play"></i> End Break
                </button>
            </form>
        {% else %}
            <div class="row">
                <div class="col-md-6">
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="break_start">
                        <input type="hidden" name="break_type" value="lunch">
                        <button type="submit" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-utensils"></i> Lunch Break
                        </button>
                    </form>
                </div>
                <div class="col-md-6">
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="break_start">
                        <input type="hidden" name="break_type" value="short">
                        <button type="submit" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-coffee"></i> Short Break
                        </button>
                    </form>
                </div>
            </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- Quick Links -->
    <div class="row mt-4">
        <div class="col-md-4">
            <a href="{% url 'attendance:my_attendance' %}" class="btn btn-outline-primary btn-block">
                <i class="fas fa-calendar-check"></i> My Attendance
            </a>
        </div>
        <div class="col-md-4">
            <a href="{% url 'leaves:my_leaves' %}" class="btn btn-outline-success btn-block">
                <i class="fas fa-calendar-times"></i> My Leaves
            </a>
        </div>
        <div class="col-md-4">
            <a href="{% url 'dashboard:home' %}" class="btn btn-outline-info btn-block">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        timeZone: 'Africa/Addis_Ababa'
    });
    document.getElementById('live-time').textContent = timeString;
}

// Update time every second
setInterval(updateTime, 1000);
updateTime(); // Initial call
</script>
{% endblock %}
