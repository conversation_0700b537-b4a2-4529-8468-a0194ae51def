{% extends 'base/base.html' %}
{% load static %}

{% block title %}Login - Ethiopian Electric Utility{% endblock %}

{% block extra_css %}
<style>
    /* Login page specific styles */
    body {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
    }

    .login-container {
        min-height: calc(100vh - 200px);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
    }

    .login-card {
        max-width: 450px;
        width: 100%;
        border: none;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }

    .login-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .login-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .login-body {
        padding: 2.5rem;
    }

    .ethiopian-colors {
        background: linear-gradient(90deg, #009639 33%, #FFDE00 33%, #FFDE00 66%, #DA020E 66%);
        height: 5px;
        width: 100%;
        margin-bottom: 1rem;
    }

    .form-control {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 15px 20px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus {
        border-color: #1e3c72;
        box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
        background: white;
    }

    .btn-login {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border: none;
        border-radius: 12px;
        padding: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
        background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
    }

    .btn-login:active {
        transform: translateY(0);
    }

    .form-label {
        font-weight: 600;
        color: #1e3c72;
        margin-bottom: 0.75rem;
    }

    .brand-icon {
        font-size: 3.5rem;
        margin-bottom: 1rem;
        color: #FFDE00;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .alert {
        border-radius: 12px;
        border: none;
        margin-bottom: 1.5rem;
    }

    .footer-text {
        text-align: center;
        margin-top: 1.5rem;
        color: #6c757d;
        font-size: 0.9rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .login-container {
            padding: 1rem;
            min-height: calc(100vh - 150px);
        }

        .login-card {
            margin: 0 1rem;
        }

        .login-header {
            padding: 1.5rem;
        }

        .login-body {
            padding: 2rem 1.5rem;
        }

        .brand-icon {
            font-size: 2.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <!-- Ethiopian Colors Bar -->
        <div class="ethiopian-colors"></div>

        <!-- Login Header -->
        <div class="login-header">
            <i class="fas fa-bolt brand-icon"></i>
            <h2 class="mb-2">Ethiopian Electric Utility</h2>
            <p class="mb-0 opacity-75">Employee Attendance Management System</p>
        </div>

        <!-- Login Body -->
        <div class="login-body">
            <form method="post" id="loginForm">
                {% csrf_token %}

                <!-- Error Messages -->
                {% if messages %}
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endfor %}
                {% endif %}

                <!-- Username Field -->
                <div class="mb-4">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>Username or Email
                    </label>
                    <input type="text" name="username" id="username" class="form-control"
                           placeholder="Enter your username or email" required autofocus>
                </div>

                <!-- Password Field -->
                <div class="mb-4">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>Password
                    </label>
                    <input type="password" name="password" id="password" class="form-control"
                           placeholder="Enter your password" required>
                </div>

                <!-- Remember Me -->
                <div class="mb-4">
                    <div class="form-check">
                        <input type="checkbox" name="remember_me" id="remember_me" class="form-check-input">
                        <label class="form-check-label" for="remember_me">
                            Remember me on this device
                        </label>
                    </div>
                </div>

                <!-- Login Button -->
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>Login to System
                    </button>
                </div>
            </form>

            <!-- Footer Text -->
            <div class="footer-text">
                <small>
                    <i class="fas fa-shield-alt me-1"></i>
                    Secure access to EEU attendance system<br>
                    Contact IT support for account assistance
                </small>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on username field
    document.getElementById('username').focus();

    // Add loading state to login button
    const form = document.getElementById('loginForm');
    const loginBtn = document.querySelector('.btn-login');

    form.addEventListener('submit', function() {
        loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Logging in...';
        loginBtn.disabled = true;
    });

    // Add enter key support
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            form.submit();
        }
    });
});
</script>
{% endblock %}
