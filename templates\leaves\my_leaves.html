{% extends 'base/base.html' %}
{% load static %}

{% block title %}My Leaves - EEU Attendance{% endblock %}

{% block extra_css %}
<style>
    .balance-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .balance-item {
        text-align: center;
        padding: 1.5rem;
        border-right: 1px solid #dee2e6;
    }
    
    .balance-item:last-child {
        border-right: none;
    }
    
    .balance-number {
        font-size: 2rem;
        font-weight: bold;
        color: #1e3c72;
    }
    
    .leave-table {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-approved { background-color: #d4edda; color: #155724; }
    .status-rejected { background-color: #f8d7da; color: #721c24; }
    .status-cancelled { background-color: #e2e3e5; color: #383d41; }
    
    .leave-type-badge {
        background-color: #e3f2fd;
        color: #1976d2;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="fas fa-calendar-times"></i> My Leave Requests</h2>
                <p class="text-muted">Manage your leave requests and view balances</p>
            </div>
            <a href="{% url 'leaves:request' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Leave Request
            </a>
        </div>
    </div>
    
    <!-- Leave Balances -->
    <div class="card balance-card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Leave Balances ({{ current_year }})</h5>
        </div>
        <div class="card-body p-0">
            {% if leave_balances %}
            <div class="row no-gutters">
                {% for balance in leave_balances %}
                <div class="col-md-3 balance-item">
                    <div class="balance-number">{{ balance.available_days }}</div>
                    <div class="text-muted">{{ balance.leave_type.name }}</div>
                    <small class="text-muted">
                        Used: {{ balance.used_days }} / {{ balance.allocated_days }}
                    </small>
                    <div class="progress mt-2" style="height: 5px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {% widthratio balance.used_days balance.allocated_days 100 %}%">
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                <p class="text-muted mb-0">No leave balances configured for {{ current_year }}</p>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Leave Requests -->
    <div class="card leave-table">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list"></i> Leave Request History</h5>
            <small>Total Requests: {{ page_obj.paginator.count }}</small>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Leave Type</th>
                            <th>Duration</th>
                            <th>Dates</th>
                            <th>Days</th>
                            <th>Status</th>
                            <th>Applied On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave_request in page_obj %}
                        <tr>
                            <td>
                                <span class="leave-type-badge">{{ leave_request.leave_type.name }}</span>
                            </td>
                            <td>
                                <small class="text-muted">{{ leave_request.get_duration_type_display }}</small>
                            </td>
                            <td>
                                <strong>{{ leave_request.start_date|date:"M d" }}</strong>
                                {% if leave_request.start_date != leave_request.end_date %}
                                    - <strong>{{ leave_request.end_date|date:"M d, Y" }}</strong>
                                {% else %}
                                    <strong>{{ leave_request.start_date|date:", Y" }}</strong>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ leave_request.total_days }} day{{ leave_request.total_days|pluralize }}</span>
                            </td>
                            <td>
                                <span class="status-badge status-{{ leave_request.status }}">
                                    {{ leave_request.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <small>{{ leave_request.applied_on|date:"M d, Y" }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'leaves:detail' leave_request.id %}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if leave_request.status == 'pending' %}
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            onclick="cancelLeave({{ leave_request.id }})">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="Leave requests pagination">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5>No leave requests found</h5>
                <p class="text-muted">You haven't submitted any leave requests yet.</p>
                <a href="{% url 'leaves:request' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Submit Your First Request
                </a>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-md-4">
            <a href="{% url 'leaves:request' %}" class="btn btn-success btn-block">
                <i class="fas fa-plus"></i> New Leave Request
            </a>
        </div>
        <div class="col-md-4">
            <a href="{% url 'attendance:my_attendance' %}" class="btn btn-info btn-block">
                <i class="fas fa-calendar-check"></i> My Attendance
            </a>
        </div>
        <div class="col-md-4">
            <a href="{% url 'dashboard:home' %}" class="btn btn-primary btn-block">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Cancel Leave Modal -->
<div class="modal fade" id="cancelLeaveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Leave Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this leave request?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No, Keep It</button>
                <form method="post" id="cancelForm" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Yes, Cancel Request</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function cancelLeave(leaveId) {
    const modal = new bootstrap.Modal(document.getElementById('cancelLeaveModal'));
    const form = document.getElementById('cancelForm');
    form.action = `/leaves/cancel/${leaveId}/`;
    modal.show();
}
</script>
{% endblock %}
