from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from employees.models import Employee


class LeaveType(models.Model):
    """
    Types of leaves available in the system
    """
    name = models.CharField(max_length=50, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    
    # Leave allocation settings
    max_days_per_year = models.IntegerField(default=0)
    max_consecutive_days = models.IntegerField(default=0)
    is_paid = models.BooleanField(default=True)
    requires_approval = models.BooleanField(default=True)
    
    # Advance notice requirements
    min_notice_days = models.IntegerField(default=1, help_text="Minimum days notice required")
    
    # Gender specific (for maternity/paternity leave)
    is_gender_specific = models.BooleanField(default=False)
    applicable_gender = models.CharField(
        max_length=10, 
        choices=[('male', 'Male'), ('female', 'Female')], 
        blank=True
    )
    
    # Ethiopian specific leave types
    is_ethiopian_specific = models.BooleanField(default=False)
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Leave Type'
        verbose_name_plural = 'Leave Types'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.code})"


class LeaveBalance(models.Model):
    """
    Employee leave balance tracking
    """
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leave_balances')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    year = models.IntegerField(default=timezone.now().year)
    
    # Balance tracking
    allocated_days = models.DecimalField(max_digits=5, decimal_places=1, default=0.0)
    used_days = models.DecimalField(max_digits=5, decimal_places=1, default=0.0)
    pending_days = models.DecimalField(max_digits=5, decimal_places=1, default=0.0)
    
    # Carry forward from previous year
    carried_forward_days = models.DecimalField(max_digits=5, decimal_places=1, default=0.0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Leave Balance'
        verbose_name_plural = 'Leave Balances'
        unique_together = ['employee', 'leave_type', 'year']
        ordering = ['employee', 'year', 'leave_type']
    
    def __str__(self):
        return f"{self.employee} - {self.leave_type} ({self.year})"
    
    @property
    def available_days(self):
        return self.allocated_days + self.carried_forward_days - self.used_days - self.pending_days
    
    @property
    def total_allocated(self):
        return self.allocated_days + self.carried_forward_days


class LeaveRequest(models.Model):
    """
    Employee leave requests
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled'),
        ('withdrawn', 'Withdrawn'),
    ]
    
    DURATION_TYPE_CHOICES = [
        ('full_day', 'Full Day'),
        ('half_day_morning', 'Half Day - Morning'),
        ('half_day_afternoon', 'Half Day - Afternoon'),
        ('hourly', 'Hourly'),
    ]
    
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leave_requests')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    
    # Leave period
    start_date = models.DateField()
    end_date = models.DateField()
    duration_type = models.CharField(max_length=20, choices=DURATION_TYPE_CHOICES, default='full_day')
    total_days = models.DecimalField(max_digits=5, decimal_places=1, default=0.0)
    
    # Request details
    reason = models.TextField()
    emergency_contact = models.CharField(max_length=100, blank=True)
    emergency_phone = models.CharField(max_length=15, blank=True)
    
    # Approval workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    applied_on = models.DateTimeField(auto_now_add=True)
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leave_requests'
    )
    approved_on = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    
    # Supporting documents
    supporting_document = models.FileField(upload_to='leave_documents/', null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Leave Request'
        verbose_name_plural = 'Leave Requests'
        ordering = ['-applied_on']
    
    def __str__(self):
        return f"{self.employee} - {self.leave_type} ({self.start_date} to {self.end_date})"
    
    def save(self, *args, **kwargs):
        # Calculate total days
        if self.start_date and self.end_date:
            days_diff = (self.end_date - self.start_date).days + 1
            
            if self.duration_type == 'full_day':
                self.total_days = days_diff
            elif self.duration_type in ['half_day_morning', 'half_day_afternoon']:
                self.total_days = days_diff * 0.5
            # For hourly, total_days should be calculated separately
        
        super().save(*args, **kwargs)
    
    @property
    def is_pending(self):
        return self.status == 'pending'
    
    @property
    def is_approved(self):
        return self.status == 'approved'
    
    @property
    def can_be_cancelled(self):
        return self.status in ['pending', 'approved'] and self.start_date > timezone.now().date()


class LeaveApprovalWorkflow(models.Model):
    """
    Leave approval workflow steps
    """
    leave_request = models.ForeignKey(
        LeaveRequest, 
        on_delete=models.CASCADE, 
        related_name='approval_steps'
    )
    approver = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    step_order = models.IntegerField(default=1)
    is_required = models.BooleanField(default=True)
    
    # Approval details
    approved = models.BooleanField(null=True, blank=True)  # None=Pending, True=Approved, False=Rejected
    approved_on = models.DateTimeField(null=True, blank=True)
    comments = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Leave Approval Workflow'
        verbose_name_plural = 'Leave Approval Workflows'
        unique_together = ['leave_request', 'approver', 'step_order']
        ordering = ['leave_request', 'step_order']
    
    def __str__(self):
        status = "Pending"
        if self.approved is True:
            status = "Approved"
        elif self.approved is False:
            status = "Rejected"
        return f"{self.leave_request} - Step {self.step_order} ({status})"


class LeaveEncashment(models.Model):
    """
    Leave encashment requests (converting leave to cash)
    """
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leave_encashments')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    year = models.IntegerField(default=timezone.now().year)
    
    days_to_encash = models.DecimalField(max_digits=5, decimal_places=1)
    rate_per_day = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    
    status = models.CharField(
        max_length=20, 
        choices=[
            ('pending', 'Pending'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected'),
            ('paid', 'Paid'),
        ], 
        default='pending'
    )
    
    requested_on = models.DateTimeField(auto_now_add=True)
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_encashments'
    )
    approved_on = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        verbose_name = 'Leave Encashment'
        verbose_name_plural = 'Leave Encashments'
        ordering = ['-requested_on']
    
    def __str__(self):
        return f"{self.employee} - {self.days_to_encash} days ({self.year})"
    
    def save(self, *args, **kwargs):
        self.total_amount = self.days_to_encash * self.rate_per_day
        super().save(*args, **kwargs)
