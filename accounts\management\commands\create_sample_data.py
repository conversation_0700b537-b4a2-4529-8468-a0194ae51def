from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from employees.models import Department, Position, Employee
from leaves.models import LeaveType, LeaveBalance
from attendance.models import Holiday, AttendancePolicy
from django.utils import timezone
from datetime import date, timedelta

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample data for Ethiopian Electric Utility Attendance System'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample data...'))

        # Create departments
        self.create_departments()
        
        # Create positions
        self.create_positions()
        
        # Create leave types
        self.create_leave_types()
        
        # Create holidays
        self.create_holidays()
        
        # Create attendance policy
        self.create_attendance_policy()
        
        # Create sample users and employees
        self.create_sample_users()

        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))

    def create_departments(self):
        departments = [
            {'name': 'Human Resources', 'code': 'HR', 'description': 'Human Resources Department'},
            {'name': 'Information Technology', 'code': 'IT', 'description': 'IT Department'},
            {'name': 'Finance', 'code': 'FIN', 'description': 'Finance Department'},
            {'name': 'Operations', 'code': 'OPS', 'description': 'Operations Department'},
            {'name': 'Engineering', 'code': 'ENG', 'description': 'Engineering Department'},
            {'name': 'Customer Service', 'code': 'CS', 'description': 'Customer Service Department'},
        ]
        
        for dept_data in departments:
            dept, created = Department.objects.get_or_create(
                code=dept_data['code'],
                defaults=dept_data
            )
            if created:
                self.stdout.write(f'Created department: {dept.name}')

    def create_positions(self):
        positions = [
            {'title': 'HR Manager', 'code': 'HRM', 'department_code': 'HR', 'level': 4},
            {'title': 'HR Officer', 'code': 'HRO', 'department_code': 'HR', 'level': 2},
            {'title': 'IT Manager', 'code': 'ITM', 'department_code': 'IT', 'level': 4},
            {'title': 'Software Developer', 'code': 'DEV', 'department_code': 'IT', 'level': 3},
            {'title': 'System Administrator', 'code': 'SYSADM', 'department_code': 'IT', 'level': 3},
            {'title': 'Finance Manager', 'code': 'FINM', 'department_code': 'FIN', 'level': 4},
            {'title': 'Accountant', 'code': 'ACC', 'department_code': 'FIN', 'level': 2},
            {'title': 'Operations Manager', 'code': 'OPSM', 'department_code': 'OPS', 'level': 4},
            {'title': 'Technician', 'code': 'TECH', 'department_code': 'OPS', 'level': 2},
            {'title': 'Engineer', 'code': 'ENG', 'department_code': 'ENG', 'level': 3},
        ]
        
        for pos_data in positions:
            department = Department.objects.get(code=pos_data['department_code'])
            pos, created = Position.objects.get_or_create(
                code=pos_data['code'],
                defaults={
                    'title': pos_data['title'],
                    'department': department,
                    'level': pos_data['level']
                }
            )
            if created:
                self.stdout.write(f'Created position: {pos.title}')

    def create_leave_types(self):
        leave_types = [
            {
                'name': 'Annual Leave',
                'code': 'AL',
                'max_days_per_year': 20,
                'max_consecutive_days': 15,
                'is_paid': True,
                'min_notice_days': 7
            },
            {
                'name': 'Sick Leave',
                'code': 'SL',
                'max_days_per_year': 10,
                'max_consecutive_days': 5,
                'is_paid': True,
                'min_notice_days': 1
            },
            {
                'name': 'Maternity Leave',
                'code': 'ML',
                'max_days_per_year': 120,
                'max_consecutive_days': 120,
                'is_paid': True,
                'is_gender_specific': True,
                'applicable_gender': 'female',
                'min_notice_days': 30
            },
            {
                'name': 'Paternity Leave',
                'code': 'PL',
                'max_days_per_year': 10,
                'max_consecutive_days': 10,
                'is_paid': True,
                'is_gender_specific': True,
                'applicable_gender': 'male',
                'min_notice_days': 7
            },
            {
                'name': 'Emergency Leave',
                'code': 'EL',
                'max_days_per_year': 5,
                'max_consecutive_days': 3,
                'is_paid': False,
                'min_notice_days': 0
            },
        ]
        
        for leave_data in leave_types:
            leave_type, created = LeaveType.objects.get_or_create(
                code=leave_data['code'],
                defaults=leave_data
            )
            if created:
                self.stdout.write(f'Created leave type: {leave_type.name}')

    def create_holidays(self):
        current_year = timezone.now().year
        holidays = [
            {'name': 'New Year', 'date': date(current_year, 1, 1), 'holiday_type': 'public'},
            {'name': 'Epiphany', 'date': date(current_year, 1, 19), 'holiday_type': 'religious'},
            {'name': 'Victory of Adwa', 'date': date(current_year, 3, 2), 'holiday_type': 'national'},
            {'name': 'Good Friday', 'date': date(current_year, 4, 14), 'holiday_type': 'religious'},
            {'name': 'Easter Sunday', 'date': date(current_year, 4, 16), 'holiday_type': 'religious'},
            {'name': 'Labour Day', 'date': date(current_year, 5, 1), 'holiday_type': 'public'},
            {'name': 'Patriots Day', 'date': date(current_year, 5, 5), 'holiday_type': 'national'},
            {'name': 'Derg Downfall Day', 'date': date(current_year, 5, 28), 'holiday_type': 'national'},
            {'name': 'Ethiopian New Year', 'date': date(current_year, 9, 11), 'holiday_type': 'national'},
            {'name': 'Finding of True Cross', 'date': date(current_year, 9, 27), 'holiday_type': 'religious'},
        ]
        
        for holiday_data in holidays:
            holiday, created = Holiday.objects.get_or_create(
                name=holiday_data['name'],
                date=holiday_data['date'],
                defaults=holiday_data
            )
            if created:
                self.stdout.write(f'Created holiday: {holiday.name}')

    def create_attendance_policy(self):
        policy, created = AttendancePolicy.objects.get_or_create(
            name='Standard EEU Policy',
            defaults={
                'description': 'Standard attendance policy for Ethiopian Electric Utility',
                'standard_work_hours': 8.0,
                'grace_period_minutes': 15,
                'minimum_hours_half_day': 4.0,
                'lunch_break_minutes': 60,
                'tea_break_minutes': 15,
                'overtime_rate_multiplier': 1.5,
                'max_overtime_hours_per_day': 4.0,
            }
        )
        if created:
            self.stdout.write(f'Created attendance policy: {policy.name}')

    def create_sample_users(self):
        # Create admin user
        admin_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'admin',
                'first_name': 'System',
                'last_name': 'Administrator',
                'employee_id': 'EEU001',
                'role': 'admin',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write(f'Created admin user: {admin_user.email}')

        # Create HR manager
        hr_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'hrmanager',
                'first_name': 'Almaz',
                'last_name': 'Tadesse',
                'employee_id': 'EEU002',
                'role': 'hr',
                'full_name_amharic': 'አልማዝ ታደሰ',
            }
        )
        if created:
            hr_user.set_password('hr123')
            hr_user.save()
            self.stdout.write(f'Created HR user: {hr_user.email}')

        # Create sample employee
        emp_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'employee',
                'first_name': 'Dawit',
                'last_name': 'Bekele',
                'employee_id': 'EEU003',
                'role': 'employee',
                'full_name_amharic': 'ዳዊት በቀለ',
            }
        )
        if created:
            emp_user.set_password('emp123')
            emp_user.save()
            self.stdout.write(f'Created employee user: {emp_user.email}')

        self.stdout.write(self.style.WARNING('Default passwords:'))
        self.stdout.write('Admin: admin123')
        self.stdout.write('HR: hr123')
        self.stdout.write('Employee: emp123')
