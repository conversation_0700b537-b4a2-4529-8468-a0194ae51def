{% extends 'base/base.html' %}
{% load static %}

{% block title %}My Attendance - EEU Attendance{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #1e3c72;
    }
    
    .attendance-table {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .status-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-present { background-color: #d4edda; color: #155724; }
    .status-late { background-color: #fff3cd; color: #856404; }
    .status-absent { background-color: #f8d7da; color: #721c24; }
    
    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-calendar-check"></i> My Attendance History</h2>
            <p class="text-muted">View your attendance records and statistics</p>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="stats-number text-success">{{ present_days }}</div>
                    <div class="text-muted">Present Days</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="stats-number text-warning">{{ late_days }}</div>
                    <div class="text-muted">Late Days</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="stats-number text-danger">{{ absent_days }}</div>
                    <div class="text-muted">Absent Days</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <div class="stats-number text-info">{{ attendance_rate }}%</div>
                    <div class="text-muted">Attendance Rate</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Additional Stats -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card stats-card">
                <div class="card-body">
                    <h6><i class="fas fa-clock"></i> Total Hours This Month</h6>
                    <div class="stats-number">{{ total_hours|floatformat:1 }}</div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card stats-card">
                <div class="card-body">
                    <h6><i class="fas fa-plus-circle"></i> Overtime Hours</h6>
                    <div class="stats-number">{{ total_overtime|floatformat:1 }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filter Section -->
    <div class="filter-section">
        <form method="get" class="row align-items-end">
            <div class="col-md-4">
                <label for="month" class="form-label">Month</label>
                <select name="month" id="month" class="form-select">
                    {% for i in "123456789012"|make_list %}
                        <option value="{{ forloop.counter }}" {% if month == forloop.counter %}selected{% endif %}>
                            {{ forloop.counter|date:"F" }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="year" class="form-label">Year</label>
                <select name="year" id="year" class="form-select">
                    {% for y in "2023,2024,2025"|split:"," %}
                        <option value="{{ y }}" {% if year|stringformat:"s" == y %}selected{% endif %}>{{ y }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <a href="{% url 'attendance:my_attendance' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-undo"></i> Reset
                </a>
            </div>
        </form>
    </div>
    
    <!-- Attendance Records Table -->
    <div class="card attendance-table">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-table"></i> Attendance Records</h5>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Date</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th>Total Hours</th>
                            <th>Overtime</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in page_obj %}
                        <tr>
                            <td>
                                <strong>{{ record.date|date:"M d, Y" }}</strong><br>
                                <small class="text-muted">{{ record.date|date:"l" }}</small>
                            </td>
                            <td>
                                {% if record.check_in_time %}
                                    {{ record.check_in_time|date:"H:i" }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.check_out_time %}
                                    {{ record.check_out_time|date:"H:i" }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.total_hours %}
                                    {{ record.total_hours|floatformat:1 }}h
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.overtime_hours %}
                                    <span class="text-success">+{{ record.overtime_hours|floatformat:1 }}h</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="status-badge status-{{ record.status }}">
                                    {{ record.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <a href="{% url 'attendance:detail' record.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="Attendance pagination">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}&month={{ month }}&year={{ year }}">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}&month={{ month }}&year={{ year }}">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5>No attendance records found</h5>
                <p class="text-muted">No attendance records for the selected period.</p>
                <a href="{% url 'attendance:clock' %}" class="btn btn-primary">
                    <i class="fas fa-clock"></i> Clock In Now
                </a>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-md-4">
            <a href="{% url 'attendance:clock' %}" class="btn btn-success btn-block">
                <i class="fas fa-clock"></i> Clock In/Out
            </a>
        </div>
        <div class="col-md-4">
            <a href="{% url 'leaves:request' %}" class="btn btn-warning btn-block">
                <i class="fas fa-calendar-plus"></i> Request Leave
            </a>
        </div>
        <div class="col-md-4">
            <a href="{% url 'dashboard:home' %}" class="btn btn-info btn-block">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}
