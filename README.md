# Ethiopian Electric Utility - Employee Attendance Management System

A comprehensive Django-based employee attendance management system designed specifically for Ethiopian Electric Utility, featuring MySQL database integration, role-based access control, and Ethiopian timezone support.

## Features

### Core Functionality
- **Employee Registration & Profile Management**
- **Clock In/Out with Timestamp Tracking**
- **Daily, Weekly, and Monthly Attendance Reports**
- **Leave Request Management** (Sick leave, vacation, etc.)
- **Admin Dashboard for HR Management**
- **Employee Self-Service Portal**

### Technical Features
- Django 4.2 with MySQL database
- Django REST Framework for API endpoints
- Role-based permissions (Admin, HR, Employee, Supervisor)
- Responsive web interface using Bootstrap 5
- Ethiopian timezone (Africa/Addis_Ababa) support
- Data validation and error handling
- CSV/PDF export functionality
- CSRF protection and secure sessions

### Database Models
- **Custom User Model** with Ethiopian-specific fields
- **Employee Management** with department and position tracking
- **Attendance Records** with break time tracking
- **Leave Management** with approval workflows
- **Department & Position Management**
- **Work Schedule Management**

## Prerequisites

### System Requirements
- Python 3.8 or higher
- MySQL 8.0 or higher
- pip (Python package installer)
- Virtual environment (recommended)

### MySQL Setup

#### 1. Install MySQL Server

**Windows:**
1. Download MySQL Installer from [MySQL Official Website](https://dev.mysql.com/downloads/installer/)
2. Run the installer and choose "Developer Default" setup
3. Follow the installation wizard
4. Set root password during installation

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

**macOS:**
```bash
brew install mysql
brew services start mysql
mysql_secure_installation
```

#### 2. Create Database and User

Connect to MySQL as root:
```bash
mysql -u root -p
```

Create database and user:
```sql
-- Create database
CREATE DATABASE eeu_attendance_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (replace 'your_password' with a strong password)
CREATE USER 'eeu_user'@'localhost' IDENTIFIED BY 'your_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON eeu_attendance_db.* TO 'eeu_user'@'localhost';
FLUSH PRIVILEGES;

-- Exit MySQL
EXIT;
```

#### 3. Install MySQL Client Libraries

**Windows:**
```bash
# Install Microsoft C++ Build Tools first if not installed
pip install mysqlclient
```

**Ubuntu/Debian:**
```bash
sudo apt-get install python3-dev default-libmysqlclient-dev build-essential
pip install mysqlclient
```

**macOS:**
```bash
brew install mysql-client
export PATH="/usr/local/opt/mysql-client/bin:$PATH"
pip install mysqlclient
```

## Installation & Setup

### 1. Clone and Setup Project

```bash
# Clone the repository (if using git)
git clone <repository-url>
cd eeu_attendance

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Configuration

Create a `.env` file in the project root:

```env
# Database Configuration
DB_NAME=eeu_attendance_db
DB_USER=eeu_user
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306

# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Email Configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

### 3. Database Migration

```bash
# Create migrations
python manage.py makemigrations accounts
python manage.py makemigrations employees
python manage.py makemigrations attendance
python manage.py makemigrations leaves

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

### 4. Load Initial Data (Optional)

```bash
# Load sample data
python manage.py loaddata fixtures/departments.json
python manage.py loaddata fixtures/positions.json
python manage.py loaddata fixtures/leave_types.json
python manage.py loaddata fixtures/holidays.json
```

### 5. Collect Static Files

```bash
python manage.py collectstatic
```

### 6. Run Development Server

```bash
python manage.py runserver
```

Visit `http://127.0.0.1:8000` to access the application.

## Usage

### Admin Access
- URL: `http://127.0.0.1:8000/admin/`
- Login with superuser credentials created during setup

### Employee Portal
- URL: `http://127.0.0.1:8000/`
- Employees can clock in/out, view attendance, request leaves

### API Endpoints
- Base URL: `http://127.0.0.1:8000/api/`
- Authentication required for most endpoints
- Documentation available at `/api/docs/`

## Project Structure

```
eeu_attendance/
├── accounts/           # User management and authentication
├── employees/          # Employee profiles and department management
├── attendance/         # Attendance tracking and reporting
├── leaves/            # Leave request management
├── reports/           # Reporting and analytics
├── dashboard/         # Dashboard views
├── templates/         # HTML templates
├── static/           # CSS, JavaScript, images
├── media/            # User uploaded files
├── fixtures/         # Initial data files
├── logs/             # Application logs
└── requirements.txt  # Python dependencies
```

## Configuration

### MySQL Database Settings

The system is configured to use MySQL by default. Database settings in `settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ.get('DB_NAME', 'eeu_attendance_db'),
        'USER': os.environ.get('DB_USER', 'root'),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '3306'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
    }
}
```

### Ethiopian Timezone Configuration

```python
TIME_ZONE = 'Africa/Addis_Ababa'
USE_TZ = True
```

## Troubleshooting

### Common MySQL Issues

1. **Connection Error:**
   - Verify MySQL service is running
   - Check database credentials in `.env` file
   - Ensure database and user exist

2. **mysqlclient Installation Error:**
   - Install MySQL development headers
   - Use alternative: `pip install PyMySQL` and add to settings:
     ```python
     import pymysql
     pymysql.install_as_MySQLdb()
     ```

3. **Character Encoding Issues:**
   - Ensure database uses `utf8mb4` charset
   - Check MySQL configuration for proper UTF-8 support

### Performance Optimization

1. **Database Indexing:**
   - Indexes are automatically created for foreign keys
   - Consider additional indexes for frequently queried fields

2. **Query Optimization:**
   - Use `select_related()` and `prefetch_related()` for related objects
   - Implement database query caching

## Security Considerations

- Change default SECRET_KEY in production
- Use environment variables for sensitive data
- Enable HTTPS in production
- Regular database backups
- Implement proper user permissions
- Monitor access logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and add tests
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For technical support or questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation wiki

---

**Ethiopian Electric Utility - Employee Attendance Management System**  
Version 1.0.0 | Built with Django 4.2 & MySQL
