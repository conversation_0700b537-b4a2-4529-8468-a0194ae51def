# Generated by Django 4.2.7 on 2025-05-28 13:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
                ('max_days_per_year', models.IntegerField(default=0)),
                ('max_consecutive_days', models.IntegerField(default=0)),
                ('is_paid', models.BooleanField(default=True)),
                ('requires_approval', models.Boolean<PERSON>ield(default=True)),
                ('min_notice_days', models.IntegerField(default=1, help_text='Minimum days notice required')),
                ('is_gender_specific', models.Bo<PERSON>anField(default=False)),
                ('applicable_gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=10)),
                ('is_ethiopian_specific', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Leave Type',
                'verbose_name_plural': 'Leave Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('duration_type', models.CharField(choices=[('full_day', 'Full Day'), ('half_day_morning', 'Half Day - Morning'), ('half_day_afternoon', 'Half Day - Afternoon'), ('hourly', 'Hourly')], default='full_day', max_length=20)),
                ('total_days', models.DecimalField(decimal_places=1, default=0.0, max_digits=5)),
                ('reason', models.TextField()),
                ('emergency_contact', models.CharField(blank=True, max_length=100)),
                ('emergency_phone', models.CharField(blank=True, max_length=15)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled'), ('withdrawn', 'Withdrawn')], default='pending', max_length=20)),
                ('applied_on', models.DateTimeField(auto_now_add=True)),
                ('approved_on', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('supporting_document', models.FileField(blank=True, null=True, upload_to='leave_documents/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leave_requests', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='employees.employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='leaves.leavetype')),
            ],
            options={
                'verbose_name': 'Leave Request',
                'verbose_name_plural': 'Leave Requests',
                'ordering': ['-applied_on'],
            },
        ),
        migrations.CreateModel(
            name='LeaveEncashment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(default=2025)),
                ('days_to_encash', models.DecimalField(decimal_places=1, max_digits=5)),
                ('rate_per_day', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('paid', 'Paid')], default='pending', max_length=20)),
                ('requested_on', models.DateTimeField(auto_now_add=True)),
                ('approved_on', models.DateTimeField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_encashments', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_encashments', to='employees.employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='leaves.leavetype')),
            ],
            options={
                'verbose_name': 'Leave Encashment',
                'verbose_name_plural': 'Leave Encashments',
                'ordering': ['-requested_on'],
            },
        ),
        migrations.CreateModel(
            name='LeaveBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(default=2025)),
                ('allocated_days', models.DecimalField(decimal_places=1, default=0.0, max_digits=5)),
                ('used_days', models.DecimalField(decimal_places=1, default=0.0, max_digits=5)),
                ('pending_days', models.DecimalField(decimal_places=1, default=0.0, max_digits=5)),
                ('carried_forward_days', models.DecimalField(decimal_places=1, default=0.0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to='employees.employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='leaves.leavetype')),
            ],
            options={
                'verbose_name': 'Leave Balance',
                'verbose_name_plural': 'Leave Balances',
                'ordering': ['employee', 'year', 'leave_type'],
                'unique_together': {('employee', 'leave_type', 'year')},
            },
        ),
        migrations.CreateModel(
            name='LeaveApprovalWorkflow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_order', models.IntegerField(default=1)),
                ('is_required', models.BooleanField(default=True)),
                ('approved', models.BooleanField(blank=True, null=True)),
                ('approved_on', models.DateTimeField(blank=True, null=True)),
                ('comments', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('leave_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approval_steps', to='leaves.leaverequest')),
            ],
            options={
                'verbose_name': 'Leave Approval Workflow',
                'verbose_name_plural': 'Leave Approval Workflows',
                'ordering': ['leave_request', 'step_order'],
                'unique_together': {('leave_request', 'approver', 'step_order')},
            },
        ),
    ]
