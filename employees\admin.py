from django.contrib import admin
from .models import Department, Position, Employee, WorkSchedule


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'head', 'parent_department', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at', 'parent_department')
    search_fields = ('name', 'code', 'description')
    ordering = ('name',)


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    list_display = ('title', 'code', 'department', 'level', 'is_active', 'created_at')
    list_filter = ('department', 'level', 'is_active', 'created_at')
    search_fields = ('title', 'code', 'description')
    ordering = ('department', 'level', 'title')


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ('employee_id', 'user', 'department', 'position', 'employment_status', 'hire_date')
    list_filter = ('department', 'position', 'employment_status', 'employment_type', 'hire_date')
    search_fields = ('employee_id', 'user__first_name', 'user__last_name', 'user__email')
    ordering = ('employee_id',)
    raw_id_fields = ('user', 'supervisor')


@admin.register(WorkSchedule)
class WorkScheduleAdmin(admin.ModelAdmin):
    list_display = ('employee', 'day_of_week', 'start_time', 'end_time', 'is_working_day', 'effective_from')
    list_filter = ('day_of_week', 'is_working_day', 'effective_from')
    search_fields = ('employee__employee_id', 'employee__user__first_name', 'employee__user__last_name')
    ordering = ('employee', 'day_of_week')
